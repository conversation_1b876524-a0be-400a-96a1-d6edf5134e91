import { NextResponse } from "next/server"
import bcrypt from "bcrypt";
import { Console } from "console";
import prisma from "@/app/lib/prisma";
import { createSession } from "@/app/lib/session";

export async function POST(Request:Request) {
    const {name,email,password,phoneNumber} = await Request.json()
    try{
        const existingUser =await prisma.admin.findUnique({
            where:{
                email:email
            }})
        if(existingUser) return NextResponse.json({message:"User already exists"},{status:400})
        const hashedPassword = await bcrypt.hash(password,10)
       const res =  await prisma.admin.create({
            data:{
                name,
                email,
                password:hashedPassword,
                phoneNumber:phoneNumber.toString(),
            }})
            await createSession({id:res.id,email,role:'admin'})
        return NextResponse.json({message:"Account created Succesfully"},{status:200})
    }
    catch(error){
        console.log(error)
        return NextResponse.json({message:"Something went wrong"},{status:500})
    }
     
}