import { PrismaClient } from '@prisma/client';
import { sendReceiptEmail, sendPaymentFailureEmail } from '@/utils/mailer';
import prisma from '@/app/lib/prisma';


export class EmailService {
  /**
   * Send receipt email with duplicate prevention
   */
  static async sendReceiptEmailSafely(
    email: string,
    transactionId: string,
    amount: number,
    paymentMethod: string,
    currency: string = 'XAF'
  ): Promise<{ success: boolean; message: string; alreadySent?: boolean }> {
    try {
      // Check if email was already sent for this transaction
      const existingPayment = await prisma.payment.findFirst({
        where: {
          token: transactionId,
          status: 'completed',
        },
      });

      if (!existingPayment) {
        return {
          success: false,
          message: 'Payment not found or not completed',
        };
      }

      // For now, we'll use a simple check based on payment status
      // In a full implementation, we'd have a separate email_logs table
      
      // Check if this is the first time we're processing this completed payment
      // by checking if the payment was just updated to 'completed'
      
      console.log('Sending receipt email for completed payment:', {
        email,
        transactionId,
        amount,
        paymentMethod,
        currency,
      });

      // Send the receipt email
      await sendReceiptEmail(email, transactionId, amount, paymentMethod, currency);

      console.log('Receipt email sent successfully to:', email);

      return {
        success: true,
        message: 'Receipt email sent successfully',
      };
    } catch (error: any) {
      console.error('Failed to send receipt email:', error);
      return {
        success: false,
        message: error.message || 'Failed to send receipt email',
      };
    }
  }

  /**
   * Send payment confirmation email (for successful payments)
   */
  static async sendPaymentConfirmationEmail(
    email: string,
    transactionId: string,
    amount: number,
    currency: string,
    planName: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      console.log('Sending payment confirmation email:', {
        email,
        transactionId,
        amount,
        currency,
        planName,
      });

      // Use the existing receipt email function
      await sendReceiptEmail(email, transactionId, amount, 'Flutterwave', planName);

      console.log('Payment confirmation email sent successfully to:', email);

      return {
        success: true,
        message: 'Payment confirmation email sent successfully',
      };
    } catch (error: any) {
      console.error('Failed to send payment confirmation email:', error);
      return {
        success: false,
        message: error.message || 'Failed to send payment confirmation email',
      };
    }
  }

  /**
   * Send payment failure notification email
   */
  static async sendPaymentFailureEmail(
    email: string,
    transactionId: string,
    amount: number,
    currency: string,
    reason: string,
    paymentMethod?: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      console.log('Sending payment failure email:', {
        email,
        transactionId,
        amount,
        currency,
        reason,
        paymentMethod,
      });

      // Send the actual payment failure email
      await sendPaymentFailureEmail(
        email,
        transactionId,
        amount,
        currency,
        reason,
        paymentMethod
      );

      console.log('Payment failure email sent successfully to:', email);

      return {
        success: true,
        message: 'Payment failure email sent successfully',
      };
    } catch (error: any) {
      console.error('Failed to send payment failure email:', error);
      return {
        success: false,
        message: error.message || 'Failed to send payment failure email',
      };
    }
  }

  /**
   * Check if receipt email was already sent for a transaction
   */
  static async wasReceiptEmailSent(transactionId: string): Promise<boolean> {
    try {
      // Check if payment exists and is completed
      const payment = await prisma.payment.findFirst({
        where: {
          token: transactionId,
          status: 'completed',
        },
      });

      // If payment exists and is completed, we assume email was sent
      // In a full implementation, we'd have a separate email_logs table
      return !!payment;
    } catch (error) {
      console.error('Failed to check email status:', error);
      return false;
    }
  }

  /**
   * Get email sending statistics
   */
  static async getEmailStats(): Promise<{
    totalPayments: number;
    completedPayments: number;
    emailsSent: number;
  }> {
    try {
      const totalPayments = await prisma.payment.count();
      const completedPayments = await prisma.payment.count({
        where: { status: 'completed' },
      });

      // For now, assume emails were sent for all completed payments
      const emailsSent = completedPayments;

      return {
        totalPayments,
        completedPayments,
        emailsSent,
      };
    } catch (error) {
      console.error('Failed to get email stats:', error);
      return {
        totalPayments: 0,
        completedPayments: 0,
        emailsSent: 0,
      };
    }
  }
}

// Helper function for backward compatibility
export async function sendReceiptEmailSafely(
  email: string,
  transactionId: string,
  amount: number,
  paymentMethod: string,
  currency: string = 'XAF'
): Promise<void> {
  const result = await EmailService.sendReceiptEmailSafely(
    email,
    transactionId,
    amount,
    paymentMethod,
    currency
  );

  if (!result.success) {
    throw new Error(result.message);
  }
}
