"use client";
import { error } from 'console';
import { Span } from 'next/dist/trace';
import React, { Dispatch, useEffect } from 'react'
import { Eye, EyeClosed, EyeOff, User } from 'lucide-react';
import { cn } from '@/app/lib/utils';
import { on } from 'events';
import { set } from 'sanity';
export type ErrorType = {
  characterLimit?: {
    min: number;
    max: number;
  };
  notEmpty?: boolean;
  onlyNumbers?: boolean;
  onlyLetters?: boolean;
  containLowerCase?: boolean;
  containUpperCase?: boolean;
  containNumber?: boolean;
  containSpecialCharacter?: boolean;
  NoSpecailcharaterType?:string
  emailValidation?: boolean;
  confirmPassword?: boolean;
}
export type InputProps = {
  IconClassName?: string;
  errorClassName?: string;
  inputClassName?: string;
  icon?:React.ReactNode;
  placeholder?: string;
  value?: string;
  OnChange?: (value: string) => void;
  className?: string;
  labelText?: string;
  error: {
    message?: string[];
    type?: ErrorType;
  } | null;
  setError?:(error: boolean) => void;
  passwordValue?:string
} & React.InputHTMLAttributes<HTMLInputElement>;

function validateInput(input: string, rules: ErrorType): string[] {
  console.log("Validating input:", input, "with rules:", rules);
  const errors: string[] = [];

  // Check for empty string
  if (rules.notEmpty && input.trim() === "") {
    errors.push("Input should not be empty.");
  }

  // Check character length
  if (rules.characterLimit) {
    const { min, max } = rules.characterLimit;
    if (input.length < min) {
      errors.push(`Input should be at least ${min} characters long.`);
    }
    if (input.length > max) {
      errors.push(`Input should not exceed ${max} characters.`);
    }
  }

  // Check only numbers
  if (rules.onlyNumbers && !/^\d+$/.test(input)) {
    errors.push("Input should contain only numbers.");
  }

  // Check only letters
  if (rules.onlyLetters && !/^[a-zA-Z]+$/.test(input)) {
    errors.push("Input should contain only letters.");
  }

  // Check for no special characters (excluding pattern)
  if (rules.NoSpecailcharaterType) {
    const regex = new RegExp(rules.NoSpecailcharaterType);
    if (regex.test(input)) {
      errors.push("Input contains disallowed special characters.");
    }
    
  }
  // ← Email format check
  if (rules.emailValidation) {
  const raw = input;
  const value = input.trim().toLowerCase();

  console.log("Validating email format for input:", `"${raw}"`, "→ after trim/lower:", `"${value}"`);

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const ok = emailRegex.test(value);
  console.log("  → regex.test result:", ok);

  if (!ok) {
    errors.push("Invalid email format.");
  } else {
    console.log("Input is valid for emailValidation rule.");
  }
}

  if (rules.containLowerCase && !/[a-z]/.test(input)) {
    errors.push("Input must contain at least one lowercase letter.");
  }

  if (rules.containUpperCase && !/[A-Z]/.test(input)) {
    errors.push("Input must contain at least one uppercase letter.");
  }

  if (rules.containNumber && !/[0-9]/.test(input)) {
    errors.push("Input must contain at least one number.");
  }

  if (rules.containSpecialCharacter && !/[!@#$%^&*(),.?":{}|<>]/.test(input)) {
    errors.push("Input must contain at least one special character.");
  }

  return errors;
}
const Input = ({className,inputClassName,icon,setError,IconClassName,error,OnChange,passwordValue,...props}:InputProps) => {
  const [errorMessages, setErrorMessages] = React.useState<string[]>(error?.message || []);
  

  // adjust the error of confirm password
  useEffect(() => {
    if (error?.type?.confirmPassword) {
      setError && setError(passwordValue !== props.value);
    } else {
      setErrorMessages([]);
      setError && setError(false);
    }
  },[error?.type?.confirmPassword])
  
  // adjust the error of none empty field
  useEffect(() => {
    if (error?.type?.notEmpty) {
      setError && setError(validateInput(props.value || "", error.type).length > 0);
    } else {
      setErrorMessages([]);
      setError && setError(false);
    }
  },[error?.type?.notEmpty])


  // handle the form change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  // 0) Get the input value
    const {name,value} = e.target;

  // 1) Determine which rules object to use:
  const rules: ErrorType =
    error?.type 
      ? error.type 
      : props.type === "email"
        ? { emailValidation: true }
        : {};

  // 2) Run validation once:
  const validationErrors = validateInput(value, rules);

  // 3) Update state:
  if (validationErrors.length > 0) {
    setErrorMessages(validationErrors);
    setError?.(true);
  } else {
    setErrorMessages([]);
    setError?.(false);
  }
  if(props.name == "confirmPassword" && passwordValue !== value){
    console.log(passwordValue,value)
    setErrorMessages(["Password doesn't match"]);
    setError?.(true);
  }

  // 4) Always propagate the new value upstream
  OnChange?.(value);
};
  const [type, setType] = React.useState(props.type || "text");
  return (
    <>
      <div className="flex  h-fit  w-full rounded-md items-end justify-between  border-input border bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm">
        <div className="gap-4 flex items-center w-full">
          <div className={cn(IconClassName)}>{icon}</div>
        <input onChange={handleChange} className={cn('outline-none bg-transparent w-full h-5',inputClassName)} {...props} type={props.type ? props.type == "password" ? type : props.type : type} />
        </div>
        {props.type && props.type === "password" && type === "password" ? (<div onClick={() => setType("text")} className={cn(IconClassName)}><EyeOff/></div>) : props.type && props.type === "password" && type === "text" ? (<div className={cn(IconClassName)} onClick={() => setType("password")}><Eye/></div>) : null}
      </div>
        {errorMessages.length > 0 && errorMessages.map((message, index) => (
          <span key={index} className={cn("text-red-600 flex items-center  text-xs", error?.type?.characterLimit ? "mt-1" : "mt-2")}>
            <div className="!size-2 bg-red-600 rounded-full"></div>
            <span className="ml-2">{message}</span>
          </span>
        ))}
        </>
  )
}

export default Input