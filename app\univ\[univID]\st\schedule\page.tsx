"use client"

import { useState } from "react"
import {
  BookOpen,
  Calendar,
  Plus,
  ChevronLeft,
  ChevronRight,
  Bell,
  HelpCircle,
  MapPin,
  Edit,
  Trash2,
  Copy,
  Mail,
  Filter,
  List,
  X,
  AlertCircle,
} from "lucide-react"

// Mock data for scheduled exams
const mockExams = [
  {
    id: 1,
    title: "Advanced Mathematics Midterm",
    course: "Mathematics",
    date: "2024-02-15",
    startTime: "10:00",
    endTime: "12:00",
    duration: 120,
    students: 45,
    location: "Room 101",
    status: "scheduled",
    proctored: true,
  },
  {
    id: 2,
    title: "Physics Quantum Quiz",
    course: "Physics",
    date: "2024-02-16",
    startTime: "14:00",
    endTime: "15:30",
    duration: 90,
    students: 38,
    location: "Online",
    status: "scheduled",
    proctored: false,
  },
  {
    id: 3,
    title: "Chemistry Lab Test",
    course: "Chemistry",
    date: "2024-02-20",
    startTime: "09:00",
    endTime: "10:30",
    duration: 90,
    students: 52,
    location: "Lab 201",
    status: "scheduled",
    proctored: true,
  },
  {
    id: 4,
    title: "Biology Final",
    course: "Biology",
    date: "2024-02-22",
    startTime: "13:00",
    endTime: "16:00",
    duration: 180,
    students: 67,
    location: "Auditorium A",
    status: "scheduled",
    proctored: true,
  },
]

export default function SchedulePage() {
  const [viewMode, setViewMode] = useState<"calendar" | "list">("calendar")
  const [currentDate, setCurrentDate] = useState(new Date(2024, 1, 15)) // February 2024
  const [courseFilter, setCourseFilter] = useState("all")
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedExam, setSelectedExam] = useState<any>(null)

  const courses = ["Mathematics", "Physics", "Chemistry", "Biology"]

  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate()
  }

  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay()
  }

  const navigateMonth = (direction: "prev" | "next") => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + (direction === "next" ? 1 : -1), 1))
  }

  const getExamsForDate = (day: number) => {
    const dateStr = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, "0")}-${String(day).padStart(2, "0")}`
    return mockExams.filter((exam) => exam.date === dateStr && (courseFilter === "all" || exam.course === courseFilter))
  }

  const filteredExams = courseFilter === "all" ? mockExams : mockExams.filter((exam) => exam.course === courseFilter)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="flex h-8 w-8 items-center justify-center rounded bg-blue-600 text-white">
                  <BookOpen className="h-4 w-4" />
                </div>
                <span className="text-xl font-bold text-blue-600">ExamPro</span>
              </div>
              <div className="hidden md:block h-6 w-px bg-gray-300"></div>
              <h1 className="text-2xl font-bold text-gray-900">Schedule</h1>
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                NEW
              </span>
            </div>
            <div className="flex items-center gap-4">
              <button className="rounded p-2 text-gray-500 hover:bg-gray-100">
                <Bell className="h-5 w-5" />
              </button>
              <button className="rounded p-2 text-gray-500 hover:bg-gray-100">
                <HelpCircle className="h-5 w-5" />
              </button>
              <button
                onClick={() => setShowCreateModal(true)}
                className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                <Plus className="h-4 w-4" />
                Schedule Exam
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex gap-6">
            {/* Main Calendar/List Area */}
            <div className="flex-1">
              {/* Controls */}
              <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    {/* View Mode Toggle */}
                    <div className="flex border border-gray-300 rounded-md">
                      <button
                        onClick={() => setViewMode("calendar")}
                        className={`px-3 py-2 text-sm font-medium rounded-l-md transition-colors ${
                          viewMode === "calendar" ? "bg-blue-600 text-white" : "bg-white text-gray-700 hover:bg-gray-50"
                        }`}
                      >
                        <Calendar className="h-4 w-4 inline mr-2" />
                        Calendar
                      </button>
                      <button
                        onClick={() => setViewMode("list")}
                        className={`px-3 py-2 text-sm font-medium rounded-r-md transition-colors ${
                          viewMode === "list" ? "bg-blue-600 text-white" : "bg-white text-gray-700 hover:bg-gray-50"
                        }`}
                      >
                        <List className="h-4 w-4 inline mr-2" />
                        List
                      </button>
                    </div>

                    {/* Course Filter */}
                    <select
                      value={courseFilter}
                      onChange={(e) => setCourseFilter(e.target.value)}
                      className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="all">All Courses</option>
                      {courses.map((course) => (
                        <option key={course} value={course}>
                          {course}
                        </option>
                      ))}
                    </select>
                  </div>

                  {viewMode === "calendar" && (
                    <div className="flex items-center gap-2">
                      <button onClick={() => navigateMonth("prev")} className="p-2 hover:bg-gray-100 rounded-md">
                        <ChevronLeft className="h-4 w-4" />
                      </button>
                      <div className="px-4 py-2 font-semibold text-gray-900">
                        {currentDate.toLocaleDateString("en-US", { month: "long", year: "numeric" })}
                      </div>
                      <button onClick={() => navigateMonth("next")} className="p-2 hover:bg-gray-100 rounded-md">
                        <ChevronRight className="h-4 w-4" />
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* Calendar or List View */}
              {viewMode === "calendar" ? (
                <CalendarView
                  currentDate={currentDate}
                  getExamsForDate={getExamsForDate}
                  onExamClick={setSelectedExam}
                />
              ) : (
                <ListView exams={filteredExams} onExamClick={setSelectedExam} />
              )}
            </div>

            {/* Sidebar */}
            <div className="w-80">
              <ExamsSidebar exams={filteredExams} />
            </div>
          </div>
        </div>
      </main>

      {/* Modals */}
      {showCreateModal && <CreateExamModal onClose={() => setShowCreateModal(false)} />}
      {selectedExam && <ExamDetailModal exam={selectedExam} onClose={() => setSelectedExam(null)} />}
    </div>
  )
}

function CalendarView({
  currentDate,
  getExamsForDate,
  onExamClick,
}: {
  currentDate: Date
  getExamsForDate: (day: number) => any[]
  onExamClick: (exam: any) => void
}) {
  const daysInMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate()
  const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1).getDay()
  const daysOfWeek = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      {/* Calendar Header */}
      <div className="grid grid-cols-7 border-b border-gray-200">
        {daysOfWeek.map((day) => (
          <div key={day} className="p-3 text-center text-sm font-medium text-gray-600 bg-gray-50">
            {day}
          </div>
        ))}
      </div>

      {/* Calendar Body */}
      <div className="grid grid-cols-7">
        {/* Empty cells for days before the first day of the month */}
        {Array.from({ length: firstDayOfMonth }, (_, index) => (
          <div key={index} className="h-24 border-b border-r border-gray-200"></div>
        ))}

        {/* Days of the month */}
        {Array.from({ length: daysInMonth }, (_, index) => {
          const day = index + 1
          const exams = getExamsForDate(day)
          const isToday =
            day === new Date().getDate() &&
            currentDate.getMonth() === new Date().getMonth() &&
            currentDate.getFullYear() === new Date().getFullYear()

          return (
            <div key={day} className="h-24 border-b border-r border-gray-200 p-1 relative overflow-hidden">
              <div className={`text-sm font-medium mb-1 ${isToday ? "text-blue-600 font-bold" : "text-gray-900"}`}>
                {day}
              </div>
              <div className="space-y-1">
                {exams.slice(0, 2).map((exam, examIndex) => (
                  <button
                    key={examIndex}
                    onClick={() => onExamClick(exam)}
                    className="w-full text-left text-xs bg-blue-100 text-blue-800 px-1 py-0.5 rounded truncate hover:bg-blue-200 transition-colors"
                  >
                    {exam.title}
                  </button>
                ))}
                {exams.length > 2 && <div className="text-xs text-gray-500">+{exams.length - 2} more</div>}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

function ListView({ exams, onExamClick }: { exams: any[]; onExamClick: (exam: any) => void }) {
  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50 border-b border-gray-200">
            <tr>
              <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                Date & Time
              </th>
              <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                Exam Title
              </th>
              <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">Course</th>
              <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                Duration
              </th>
              <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                Students
              </th>
              <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                Location
              </th>
              <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">Status</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {exams.map((exam) => (
              <tr key={exam.id} onClick={() => onExamClick(exam)} className="hover:bg-gray-50 cursor-pointer">
                <td className="py-4 px-6">
                  <div className="text-sm font-medium text-gray-900">{new Date(exam.date).toLocaleDateString()}</div>
                  <div className="text-sm text-gray-600">
                    {exam.startTime} - {exam.endTime}
                  </div>
                </td>
                <td className="py-4 px-6">
                  <div className="text-sm font-medium text-gray-900">{exam.title}</div>
                </td>
                <td className="py-4 px-6">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {exam.course}
                  </span>
                </td>
                <td className="py-4 px-6 text-sm text-gray-900">{exam.duration} min</td>
                <td className="py-4 px-6 text-sm text-gray-900">{exam.students}</td>
                <td className="py-4 px-6">
                  <div className="flex items-center gap-1 text-sm text-gray-900">
                    <MapPin className="h-3 w-3 text-gray-400" />
                    {exam.location}
                  </div>
                </td>
                <td className="py-4 px-6">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {exam.status}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

function ExamsSidebar({ exams }: { exams: any[] }) {
  const today = new Date()
  const todayStr = today.toISOString().split("T")[0]

  const examsToday = exams.filter((exam) => exam.date === todayStr)
  const upcomingExams = exams
    .filter((exam) => exam.date > todayStr)
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    .slice(0, 5)

  return (
    <div className="space-y-6">
      {/* Exams Today */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Exams Today</h3>
        {examsToday.length > 0 ? (
          <div className="space-y-3">
            {examsToday.map((exam) => (
              <div key={exam.id} className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div>
                  <div className="font-medium text-gray-900">{exam.title}</div>
                  <div className="text-sm text-gray-600">
                    {exam.startTime} - {exam.endTime}
                  </div>
                </div>
                <button className="text-blue-600 hover:text-blue-700">
                  <Bell className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 text-sm">No exams scheduled for today</p>
        )}
      </div>

      {/* Upcoming Exams */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Upcoming Exams</h3>
          <button className="text-sm text-blue-600 hover:text-blue-700">View All</button>
        </div>
        {upcomingExams.length > 0 ? (
          <div className="space-y-3">
            {upcomingExams.map((exam) => (
              <div key={exam.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium text-gray-900">{exam.title}</div>
                  <div className="text-sm text-gray-600">
                    {new Date(exam.date).toLocaleDateString()} at {exam.startTime}
                  </div>
                </div>
                <div className="text-sm text-gray-500">{exam.students} students</div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 text-sm">No upcoming exams</p>
        )}
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="space-y-3">
          <button className="w-full flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors">
            <Mail className="h-4 w-4 text-gray-400" />
            <span className="text-sm font-medium">Send Reminders</span>
          </button>
          <button className="w-full flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors">
            <Calendar className="h-4 w-4 text-gray-400" />
            <span className="text-sm font-medium">Export Schedule</span>
          </button>
          <button className="w-full flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors">
            <Filter className="h-4 w-4 text-gray-400" />
            <span className="text-sm font-medium">Advanced Filters</span>
          </button>
        </div>
      </div>
    </div>
  )
}

function CreateExamModal({ onClose }: { onClose: () => void }) {
  const [selectedTemplate, setSelectedTemplate] = useState("")

  const examTemplates = [
    { id: "1", title: "Mathematics Quiz #4", questions: 20, duration: 60 },
    { id: "2", title: "Physics Midterm", questions: 35, duration: 120 },
    { id: "3", title: "Chemistry Lab Assessment", questions: 15, duration: 90 },
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Schedule New Exam</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-5 w-5" />
          </button>
        </div>

        <form className="space-y-6">
          {/* Exam Template Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Select Exam Template</label>
            <select
              value={selectedTemplate}
              onChange={(e) => setSelectedTemplate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Choose an exam template...</option>
              {examTemplates.map((template) => (
                <option key={template.id} value={template.id}>
                  {template.title} ({template.questions} questions, {template.duration} minutes)
                </option>
              ))}
            </select>
          </div>

          {/* Date and Time */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Date</label>
              <input
                type="date"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Start Time</label>
              <input
                type="time"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Duration */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Duration (minutes)</label>
            <input
              type="number"
              min="1"
              defaultValue="60"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Location */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
            <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="online">Online</option>
              <option value="room-101">Room 101</option>
              <option value="room-102">Room 102</option>
              <option value="lab-201">Lab 201</option>
              <option value="auditorium-a">Auditorium A</option>
            </select>
          </div>

          {/* Options */}
          <div className="space-y-3">
            <div className="flex items-center">
              <input type="checkbox" id="proctored" className="h-4 w-4 text-blue-600 rounded" />
              <label htmlFor="proctored" className="ml-2 text-sm text-gray-700">
                Enable online proctoring
              </label>
            </div>
            <div className="flex items-center">
              <input type="checkbox" id="reminders" className="h-4 w-4 text-blue-600 rounded" defaultChecked />
              <label htmlFor="reminders" className="ml-2 text-sm text-gray-700">
                Send email reminders 24 hours before
              </label>
            </div>
            <div className="flex items-center">
              <input type="checkbox" id="shuffle" className="h-4 w-4 text-blue-600 rounded" />
              <label htmlFor="shuffle" className="ml-2 text-sm text-gray-700">
                Shuffle question order
              </label>
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button type="submit" className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              Schedule Exam
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

function ExamDetailModal({ exam, onClose }: { exam: any; onClose: () => void }) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Exam Details</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="space-y-6">
          {/* Exam Info */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-2">{exam.title}</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Course:</span> {exam.course}
              </div>
              <div>
                <span className="text-gray-600">Date:</span> {new Date(exam.date).toLocaleDateString()}
              </div>
              <div>
                <span className="text-gray-600">Time:</span> {exam.startTime} - {exam.endTime}
              </div>
              <div>
                <span className="text-gray-600">Duration:</span> {exam.duration} minutes
              </div>
              <div>
                <span className="text-gray-600">Students:</span> {exam.students}
              </div>
              <div>
                <span className="text-gray-600">Location:</span> {exam.location}
              </div>
            </div>
          </div>

          {/* Status */}
          <div className="flex items-center gap-2">
            <span className="text-gray-600">Status:</span>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              {exam.status}
            </span>
            {exam.proctored && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Proctored
              </span>
            )}
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4 border-t">
            <button className="flex-1 inline-flex items-center justify-center gap-2 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors">
              <Edit className="h-4 w-4" />
              Edit Exam
            </button>
            <button className="flex-1 inline-flex items-center justify-center gap-2 bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors">
              <Copy className="h-4 w-4" />
              Duplicate
            </button>
            <button className="flex-1 inline-flex items-center justify-center gap-2 bg-yellow-100 text-yellow-700 py-2 px-4 rounded-md hover:bg-yellow-200 transition-colors">
              <Mail className="h-4 w-4" />
              Send Reminder
            </button>
            <button className="flex-1 inline-flex items-center justify-center gap-2 bg-red-100 text-red-700 py-2 px-4 rounded-md hover:bg-red-200 transition-colors">
              <Trash2 className="h-4 w-4" />
              Cancel
            </button>
          </div>

          {exam.status === "scheduled" && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">Upcoming Exam</p>
                  <p className="text-sm text-yellow-700">
                    This exam is scheduled for {new Date(exam.date).toLocaleDateString()} at {exam.startTime}. Students
                    will receive email reminders 24 hours before the exam.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
