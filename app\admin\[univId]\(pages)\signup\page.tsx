"use client"

import type React from "react"

import Link from "next/link"
import { EyeOff, Mail, Lock, User, ArrowRight, Check, Key, ArrowLeft, Shield, Building } from "lucide-react"
import { useState } from "react"

export default function AdminSignupPage() {
  const [currentStep, setCurrentStep] = useState<"token" | "signup">("token")
  const [token, setToken] = useState("")
  const [isValidating, setIsValidating] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const handleTokenSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsValidating(true)

    // Simulate token validation
    await new Promise((resolve) => setTimeout(resolve, 1500))

    // For demo purposes, accept any token that's at least 6 characters
    if (token.length >= 6) {
      setCurrentStep("signup")
    } else {
      alert("Invalid token. Please enter a valid admin creation token.")
    }

    setIsValidating(false)
  }

  const handleBackToToken = () => {
    setCurrentStep("token")
    setToken("")
  }

  return (
    <div className="min-h-screen bg-gray-50 flex overflow-hidden">
      {/* Left Side - Forms Container */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-md w-full">
          {/* Forms Wrapper with Animation */}
          <div className="relative w-full">
            {/* Token Validation Form */}
            <div
              className={`w-full transition-transform duration-500 ease-in-out ${
                currentStep === "token" ? "translate-x-0" : "-translate-x-full"
              } ${currentStep === "signup" ? "absolute top-0 left-0" : ""}`}
            >
              <div className="space-y-8">
                {/* Logo and Header */}
                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-6">
                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-indigo-600 text-white">
                      <Shield className="h-6 w-6" />
                    </div>
                    <span className="text-2xl font-bold text-indigo-600">ExamPro Admin</span>
                  </div>
                  <h2 className="text-3xl font-bold text-gray-900">Admin Registration</h2>
                  <p className="mt-2 text-sm text-gray-600">Enter your admin creation token to get started</p>
                </div>

                {/* Token Form */}
                <form onSubmit={handleTokenSubmit} className="mt-8 space-y-6">
                  <div>
                    <label htmlFor="token" className="block text-sm font-medium text-gray-700 mb-1">
                      Admin Creation Token
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Key className="h-4 w-4 text-gray-400" />
                      </div>
                      <input
                        id="token"
                        name="token"
                        type="text"
                        required
                        value={token}
                        onChange={(e) => setToken(e.target.value)}
                        className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                        placeholder="Enter your admin creation token"
                      />
                    </div>
                    <p className="mt-2 text-xs text-gray-500">
                      This token is provided by the system owner or super admin
                    </p>
                  </div>

                  <div>
                    <button
                      type="submit"
                      disabled={isValidating || token.length < 6}
                      className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isValidating ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Validating...
                        </>
                      ) : (
                        <>
                          Validate Token
                          <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                        </>
                      )}
                    </button>
                  </div>

                  {/* Help Section */}
                  <div className="bg-indigo-50 rounded-md p-4">
                    <h3 className="text-sm font-medium text-indigo-800 mb-2">Need an admin token?</h3>
                    <p className="text-xs text-indigo-700 mb-3">
                      Contact the system owner or super administrator to obtain an admin creation token.
                    </p>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-xs text-indigo-700">
                        <div className="w-1.5 h-1.5 bg-indigo-500 rounded-full"></div>
                        <span>Tokens are unique per administrator</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-indigo-700">
                        <div className="w-1.5 h-1.5 bg-indigo-500 rounded-full"></div>
                        <span>Tokens expire after 24 hours</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-indigo-700">
                        <div className="w-1.5 h-1.5 bg-indigo-500 rounded-full"></div>
                        <span>One-time use only</span>
                      </div>
                    </div>
                  </div>

                  {/* Back to Login */}
                  <div className="text-center">
                    <p className="text-sm text-gray-600">
                      Already have an account?{" "}
                      <Link href="/admin/login" className="font-medium text-indigo-600 hover:text-indigo-500">
                        Sign in
                      </Link>
                    </p>
                  </div>
                </form>
              </div>
            </div>

            {/* Signup Form */}
            <div
              className={`w-full transition-transform duration-500 ease-in-out ${
                currentStep === "signup" ? "translate-x-0" : "translate-x-full"
              } ${currentStep === "token" ? "absolute top-0 left-0" : ""}`}
            >
              <div className="space-y-8">
                {/* Back Button */}
                <div className="text-center">
                  <button
                    onClick={handleBackToToken}
                    className="inline-flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-500 mb-6"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Token
                  </button>
                </div>

                {/* Logo and Header */}
                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-6">
                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-indigo-600 text-white">
                      <Shield className="h-6 w-6" />
                    </div>
                    <span className="text-2xl font-bold text-indigo-600">ExamPro Admin</span>
                  </div>

                  {/* Token Success Indicator */}
                  <div className="bg-green-50 rounded-lg p-3 mb-6">
                    <div className="flex items-center justify-center gap-2">
                      <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-100">
                        <Check className="h-4 w-4 text-green-600" />
                      </div>
                      <span className="text-sm font-medium text-green-800">Token validated successfully</span>
                    </div>
                  </div>

                  <h2 className="text-3xl font-bold text-gray-900">Create admin account</h2>
                  <p className="mt-2 text-sm text-gray-600">Complete your administrator registration</p>
                </div>

                {/* Signup Form */}
                <form className="mt-8 space-y-6">
                  <div className="space-y-4">
                    {/* Full Name Field */}
                    <div>
                      <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">
                        Full Name
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <User className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          id="fullName"
                          name="fullName"
                          type="text"
                          autoComplete="name"
                          required
                          className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                          placeholder="Enter your full name"
                        />
                      </div>
                    </div>

                    {/* Email Field */}
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        Email address
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Mail className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          id="email"
                          name="email"
                          type="email"
                          autoComplete="email"
                          required
                          className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                          placeholder="Enter your email"
                        />
                      </div>
                    </div>

                    {/* Admin Role Field */}
                    <div>
                      <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
                        Admin Role
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Building className="h-4 w-4 text-gray-400" />
                        </div>
                        <select
                          id="role"
                          name="role"
                          required
                          className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                        >
                          <option value="">Select admin role</option>
                          <option value="system_admin">System Administrator</option>
                          <option value="security_admin">Security Administrator</option>
                          <option value="university_admin">University Administrator</option>
                          <option value="support_admin">Support Administrator</option>
                        </select>
                      </div>
                    </div>

                    {/* Password Field */}
                    <div>
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                        Password
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Lock className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          id="password"
                          name="password"
                          type={showPassword ? "text" : "password"}
                          autoComplete="new-password"
                          required
                          className="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                          placeholder="Create a password"
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          <EyeOff className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                        </button>
                      </div>
                    </div>

                    {/* Confirm Password Field */}
                    <div>
                      <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                        Confirm Password
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Lock className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          id="confirmPassword"
                          name="confirmPassword"
                          type={showConfirmPassword ? "text" : "password"}
                          autoComplete="new-password"
                          required
                          className="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                          placeholder="Confirm your password"
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        >
                          <EyeOff className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Password Requirements */}
                  <div className="bg-gray-50 rounded-md p-3">
                    <p className="text-xs font-medium text-gray-700 mb-2">Password must contain:</p>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-xs text-gray-600">
                        <Check className="h-3 w-3 text-green-500" />
                        <span>At least 12 characters</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-gray-600">
                        <Check className="h-3 w-3 text-green-500" />
                        <span>One uppercase letter</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-gray-600">
                        <Check className="h-3 w-3 text-green-500" />
                        <span>One number</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-gray-600">
                        <Check className="h-3 w-3 text-green-500" />
                        <span>One special character</span>
                      </div>
                    </div>
                  </div>

                  {/* Security Notice */}
                  <div className="bg-yellow-50 rounded-md p-3">
                    <p className="text-xs font-medium text-yellow-800 mb-2">Security Notice:</p>
                    <p className="text-xs text-yellow-700">
                      Administrator accounts have extensive system access. You will be required to set up two-factor
                      authentication after login.
                    </p>
                  </div>

                  {/* Terms and Privacy */}
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="terms"
                        name="terms"
                        type="checkbox"
                        required
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="terms" className="text-gray-700">
                        I agree to the{" "}
                        <Link href="/terms" className="font-medium text-indigo-600 hover:text-indigo-500">
                          Terms of Service
                        </Link>{" "}
                        and{" "}
                        <Link href="/privacy" className="font-medium text-indigo-600 hover:text-indigo-500">
                          Privacy Policy
                        </Link>
                      </label>
                    </div>
                  </div>

                  {/* Create account button */}
                  <div>
                    <Link
                      href="/admin/dashboard"
                      className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
                    >
                      Create admin account
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </div>

                  {/* Sign in link */}
                  <div className="text-center">
                    <p className="text-sm text-gray-600">
                      Already have an account?{" "}
                      <Link href="/admin/login" className="font-medium text-indigo-600 hover:text-indigo-500">
                        Sign in
                      </Link>
                    </p>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Image/Illustration */}
      <div className="hidden lg:block relative w-0 flex-1">
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-600 to-purple-800">
          <div className="flex items-center justify-center h-full p-12">
            <div className="text-center text-white">
              <div className="mb-8">
                <div className="inline-flex items-center justify-center w-24 h-24 bg-white/10 rounded-full mb-6">
                  {currentStep === "token" ? <Key className="h-12 w-12" /> : <Shield className="h-12 w-12" />}
                </div>
                <h2 className="text-3xl font-bold mb-4">
                  {currentStep === "token" ? "Secure Admin Access" : "Complete Administrator Setup"}
                </h2>
                <p className="text-xl text-indigo-100 max-w-md">
                  {currentStep === "token"
                    ? "We use secure tokens to ensure only authorized personnel can register as system administrators."
                    : "Create your administrator account to manage the entire examination platform and all its users."}
                </p>
              </div>
              <div className="grid grid-cols-1 gap-4 max-w-sm mx-auto">
                {currentStep === "token" ? (
                  <>
                    <div className="flex items-center gap-3 text-indigo-100">
                      <div className="w-2 h-2 bg-indigo-300 rounded-full"></div>
                      <span>Enhanced security protocols</span>
                    </div>
                    <div className="flex items-center gap-3 text-indigo-100">
                      <div className="w-2 h-2 bg-indigo-300 rounded-full"></div>
                      <span>Two-factor authentication</span>
                    </div>
                    <div className="flex items-center gap-3 text-indigo-100">
                      <div className="w-2 h-2 bg-indigo-300 rounded-full"></div>
                      <span>Audit trail for all actions</span>
                    </div>
                    <div className="flex items-center gap-3 text-indigo-100">
                      <div className="w-2 h-2 bg-indigo-300 rounded-full"></div>
                      <span>Role-based access control</span>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="flex items-center gap-3 text-indigo-100">
                      <div className="w-2 h-2 bg-indigo-300 rounded-full"></div>
                      <span>Complete platform control</span>
                    </div>
                    <div className="flex items-center gap-3 text-indigo-100">
                      <div className="w-2 h-2 bg-indigo-300 rounded-full"></div>
                      <span>University management</span>
                    </div>
                    <div className="flex items-center gap-3 text-indigo-100">
                      <div className="w-2 h-2 bg-indigo-300 rounded-full"></div>
                      <span>Security monitoring</span>
                    </div>
                    <div className="flex items-center gap-3 text-indigo-100">
                      <div className="w-2 h-2 bg-indigo-300 rounded-full"></div>
                      <span>System configuration</span>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
