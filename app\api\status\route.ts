import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET() {
  try {
    const status = {
      timestamp: new Date().toISOString(),
      status: 'healthy',
      services: {
        database: 'unknown',
        email: 'unknown',
        paymentGateways: {
          flutterwave: 'unknown',
        },
      },
      statistics: {
        totalUsers: 0,
        totalSubscriptions: 0,
        activeSubscriptions: 0,
        totalPayments: 0,
        completedPayments: 0,
      },
    };

    // Test database connection
    try {
      await prisma.$queryRaw`SELECT 1`;
      status.services.database = 'healthy';
    } catch (error) {
      status.services.database = 'unhealthy';
      status.status = 'degraded';
    }

    // Check email configuration
    status.services.email = process.env.EMAIL_USER && process.env.EMAIL_PASS ? 'configured' : 'not_configured';

    // Check Flutterwave configuration
    status.services.paymentGateways = {
      flutterwave: process.env.FLUTTERWAVE_PUBLIC_KEY && process.env.FLUTTERWAVE_SECRET_KEY
        ? 'configured' : 'not_configured'
    };

    // Get statistics (only if database is healthy)
    if (status.services.database === 'healthy') {
      try {
        // Count payments (using current schema)
        status.statistics.totalPayments = await prisma.payment.count();
        status.statistics.completedPayments = await prisma.payment.count({
          where: { subscription: 'completed' },
        });

        // Count unique users by email
        const uniqueEmails = await prisma.payment.findMany({
          select: { email: true },
          distinct: ['email'],
        });
        status.statistics.totalUsers = uniqueEmails.length;

        // Count active subscriptions (simplified)
        status.statistics.activeSubscriptions = await prisma.payment.count({
          where: { subscription: 'completed' },
        });
        status.statistics.totalSubscriptions = status.statistics.activeSubscriptions;
      } catch (error) {
        console.error('Failed to get statistics:', error);
      }
    }

    return NextResponse.json(status);
  } catch (error: any) {
    console.error('Status check failed:', error);
    return NextResponse.json(
      {
        timestamp: new Date().toISOString(),
        status: 'unhealthy',
        error: 'Status check failed',
        services: {
          database: 'unknown',
          email: 'unknown',
          paymentGateways: {
            flutterwave: 'unknown',
          },
        },
      },
      { status: 500 }
    );
  }
}
