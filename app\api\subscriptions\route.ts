import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { SubscriptionService } from '@/lib/subscription-service';

const prisma = new PrismaClient();
const subscriptionService = new SubscriptionService();

// GET - Get user subscription
export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');
    const email = searchParams.get('email');

    if (!userId && !email) {
      return NextResponse.json(
        { success: false, error: 'User ID or email is required' },
        { status: 400 }
      );
    }

    // For now, we'll use email as the identifier since User model isn't available yet
    const userIdentifier = userId || email;

    if (!userIdentifier) {
      return NextResponse.json(
        { success: false, error: 'User identifier not provided' },
        { status: 400 }
      );
    }

    // Check if user has any payments (simplified subscription check)
    const userPayments = await prisma.payment.findMany({
      where: {
        email: email || userIdentifier,
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 5,
    });

    // Create a simplified subscription response
    let subscription = null;
    if (userPayments.length > 0) {
      const latestPayment = userPayments[0];
      const isActive = latestPayment.subscription === 'completed';

      if (isActive) {
        // Calculate approximate expiry date
        const createdDate = new Date(latestPayment.createdAt);
        let endDate: Date;

        if (latestPayment.subscription === 'yearly') {
          endDate = new Date(createdDate);
          endDate.setFullYear(endDate.getFullYear() + 1);
        } else {
          endDate = new Date(createdDate);
          endDate.setMonth(endDate.getMonth() + 1);
        }

        const daysRemaining = Math.ceil((endDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));

        subscription = {
          id: latestPayment.id,
          status: daysRemaining > 0 ? 'active' : 'expired',
          plan: {
            name: latestPayment.subscription === 'yearly' ? 'Yearly Plan' : 'Monthly Plan',
            price: latestPayment.amount,
            billing: latestPayment.subscription === 'yearly' ? 'yearly' : 'monthly',
          },
          startDate: latestPayment.createdAt,
          endDate: endDate,
          daysRemaining: Math.max(0, daysRemaining),
          autoRenew: true,
        };
      }
    }

    return NextResponse.json({
      success: true,
      subscription,
    });
  } catch (error: any) {
    console.error('Get subscription failed:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get subscription' },
      { status: 500 }
    );
  }
}

// POST - Create or update subscription
export async function POST(req: Request) {
  try {
    const { userId, planId, paymentId } = await req.json();

    if (!userId || !planId || !paymentId) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const subscriptionId = await subscriptionService.createSubscription({
      userId,
      planId,
      paymentId,
    });

    return NextResponse.json({
      success: true,
      subscriptionId,
    });
  } catch (error: any) {
    console.error('Create subscription failed:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to create subscription' },
      { status: 500 }
    );
  }
}

// PUT - Update subscription (cancel, renew, etc.)
export async function PUT(req: Request) {
  try {
    const { subscriptionId, action, immediate } = await req.json();

    if (!subscriptionId || !action) {
      return NextResponse.json(
        { success: false, error: 'Subscription ID and action are required' },
        { status: 400 }
      );
    }

    let result = false;

    switch (action) {
      case 'cancel':
        result = await subscriptionService.cancelSubscription(subscriptionId, immediate);
        break;
      case 'renew':
        result = await subscriptionService.renewSubscription(subscriptionId);
        break;
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: result,
      message: result ? `Subscription ${action}ed successfully` : `Failed to ${action} subscription`,
    });
  } catch (error: any) {
    console.error('Update subscription failed:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update subscription' },
      { status: 500 }
    );
  }
}
