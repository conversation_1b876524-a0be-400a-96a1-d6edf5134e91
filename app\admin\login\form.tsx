"use client"

import Link from "next/link"
import { EyeOff, Mail, Lock, ArrowRight, Shield, Loader } from "lucide-react"
import { useState } from "react"
import { toast } from "sonner"
import { useRouter } from "next/navigation"

export default function AdminLoginPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [formData,setFormData] = useState({
    email:'',
    password:'',
  })



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')
      const res = await fetch('/api/admin/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })
      if (!res.ok) {
        const err = await res.json()
        toast.error(err.message);
      }
      else{
        toast.success("Login successful")
        const router = useRouter()
        router.push('/admin/all-university')
      }
      setLoading(false)
    }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Left Side - Login Form */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {/* Logo and Header */}
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-6">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-600 text-white">
                <Shield className="h-6 w-6" />
              </div>
              <span className="text-2xl font-bold text-green-600">ExamPro Admin</span>
            </div>

            {/* Admin Login Info */}
            <div className="bg-green-50 rounded-lg p-4 mb-6">
              <div className="flex items-center justify-center gap-2">
                <Shield className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-700">System Administrator Access</span>
              </div>
            </div>

            <h2 className="text-3xl font-bold text-gray-900">Welcome back</h2>
            <p className="mt-2 text-sm text-gray-600">Sign in to your admin account</p>
          </div>

          {/* Login Form */}
          <form onSubmit={handleSubmit} className="mt-8 space-y-6">
            <div className="space-y-4">
              {/* Email Field */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Admin Email
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    onChange={(e) => {setFormData(prev => ({...prev,email:e.target.value}))}}
                    className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                    placeholder="Enter your admin email"
                  />
                </div>
              </div>

              {/* Password Field */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Password
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    onChange={(e) => {setFormData(prev => ({...prev,password:e.target.value}))}}
                    autoComplete="current-password"
                    required
                    className="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                    placeholder="Enter your password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    <EyeOff className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  </button>
                </div>
              </div>
            </div>

            {/* Remember me and Forgot password */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700">
                  Remember me
                </label>
              </div>

              <div className="text-sm">
                <Link href="/admin/forgot-password" className="font-medium text-green-600 hover:text-green-500">
                  Forgot your password?
                </Link>
              </div>
            </div>

            {/* Sign in button */}
            <div>
              <button
                type="submit"
                disabled={loading}
                className="group disabled:opacity-40 disabled:bg-green-400 relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
              >
                Sign in as Administrator
                {loading ? <Loader size={16} className="ml-2 animate-spin duration-300" /> : <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />}
              </button>
            </div>

            {/* Divider */}
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-gray-50 text-gray-500">Or</span>
              </div>
            </div>

            
            {/* Sign up link */}
            <div className="text-center">
              <p className="text-sm text-gray-600">
                Need an admin account?{" "}
                <Link href="/admin/signup" className="font-medium text-green-600 hover:text-green-500">
                  Request access
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>

      {/* Right Side - Image/Illustration */}
      <div className="hidden lg:block relative w-0 flex-1">
        <div className="absolute inset-0 bg-gradient-to-br from-green-400 to-green-800">
          <div className="flex items-center justify-center h-full p-12">
            <div className="text-center text-white">
              <div className="mb-8">
                <div className="inline-flex items-center justify-center w-24 h-24 bg-white/10 rounded-full mb-6">
                  <Shield className="h-12 w-12" />
                </div>
                <h2 className="text-3xl font-bold mb-4">Admin Control Center</h2>
                <p className="text-xl text-green-100 max-w-md">
                  Manage your entire examination platform from a single dashboard. Monitor universities, instructors,
                  and students.
                </p>
              </div>
              <div className="grid grid-cols-1 gap-4 max-w-sm mx-auto">
                <div className="flex items-center gap-3 text-green-100">
                  <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                  <span>Complete platform oversight</span>
                </div>
                <div className="flex items-center gap-3 text-green-100">
                  <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                  <span>Advanced security controls</span>
                </div>
                <div className="flex items-center gap-3 text-green-100">
                  <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                  <span>Real-time monitoring</span>
                </div>
                <div className="flex items-center gap-3 text-green-100">
                  <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                  <span>Comprehensive analytics</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
