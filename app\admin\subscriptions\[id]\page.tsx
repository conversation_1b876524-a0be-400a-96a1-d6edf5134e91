import Link from "next/link"
import { ArrowLeft, Calendar, CreditCard, User, DollarSign, Clock, CheckCircle, XCircle, AlertCircle, Mail, Phone } from "lucide-react"
import prisma from "@/app/lib/prisma"
import { verifySession } from "@/app/lib/session";
import { redirect, notFound } from "next/navigation";

type SubscriptionDetailsType = {
  id: string;
  status: string;
  activeDate: string;
  createdAt: Date;
  updatedAt: Date;
  admin: {
    id: string;
    name: string;
    email: string;
    phoneNumber: string;
  };
  plan: {
    id: string;
    name: string;
    price: number;
    currency: string;
    billing: string;
    features: string[];
    isPopular: boolean;
  } | null;
  payment: {
    id: string;
    amount: number;
    currency: string;
    status: string;
    method: string;
    transactionId: string | null;
    createdAt: Date;
    updatedAt: Date;
  } | null;
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'active':
      return <CheckCircle className="h-5 w-5 text-green-600" />
    case 'pending':
      return <Clock className="h-5 w-5 text-yellow-600" />
    case 'expired':
    case 'unactive':
      return <XCircle className="h-5 w-5 text-red-600" />
    default:
      return <AlertCircle className="h-5 w-5 text-gray-600" />
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active':
      return "bg-green-100 text-green-800"
    case 'pending':
      return "bg-yellow-100 text-yellow-800"
    case 'expired':
    case 'unactive':
      return "bg-red-100 text-red-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

export default async function SubscriptionDetailsPage({ params }: { params: { id: string } }) {
  const user = await verifySession()
  if(!user) redirect('/login')
  
  // Fetch subscription details
  const subscription = await prisma.subscription.findUnique({
    where: { id: params.id },
    select: {
      id: true,
      status: true,
      activeDate: true,
      createdAt: true,
      updatedAt: true,
      admin: {
        select: {
          id: true,
          name: true,
          email: true,
          phoneNumber: true
        }
      },
      plan: {
        select: {
          id: true,
          name: true,
          price: true,
          currency: true,
          billing: true,
          features: true,
          isPopular: true
        }
      },
      payment: {
        select: {
          id: true,
          amount: true,
          currency: true,
          status: true,
          method: true,
          transactionId: true,
          createdAt: true,
          updatedAt: true
        }
      }
    }
  })

  if (!subscription) {
    notFound()
  }

  // Calculate subscription duration and expiry
  const startDate = new Date(subscription.activeDate)
  let endDate: Date | null = null
  let daysRemaining = 0

  if (subscription.plan && subscription.status === 'active') {
    endDate = new Date(startDate)
    if (subscription.plan.billing === 'yearly') {
      endDate.setFullYear(endDate.getFullYear() + 1)
    } else {
      endDate.setMonth(endDate.getMonth() + 1)
    }
    daysRemaining = Math.ceil((endDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
  }

  return (
    <div className="min-h-screen w-full bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Link 
                href="/admin/subscriptions"
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Subscriptions
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-lg font-semibold text-gray-900">Subscription Details</h1>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Main Details */}
          <div className="lg:col-span-2 space-y-6">
            
            {/* Subscription Overview */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Subscription Overview</h2>
                <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(subscription.status)}`}>
                  {getStatusIcon(subscription.status)}
                  {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Subscription ID</label>
                  <p className="text-sm text-gray-900 font-mono">{subscription.id}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Active Date</label>
                  <p className="text-sm text-gray-900">{subscription.activeDate}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Created</label>
                  <p className="text-sm text-gray-900">{subscription.createdAt.toLocaleDateString()}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Last Updated</label>
                  <p className="text-sm text-gray-900">{subscription.updatedAt.toLocaleDateString()}</p>
                </div>

                {endDate && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-600 mb-1">Expires</label>
                      <p className="text-sm text-gray-900">{endDate.toLocaleDateString()}</p>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-600 mb-1">Days Remaining</label>
                      <p className={`text-sm font-medium ${daysRemaining > 7 ? 'text-green-600' : daysRemaining > 0 ? 'text-yellow-600' : 'text-red-600'}`}>
                        {Math.max(0, daysRemaining)} days
                      </p>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Plan Details */}
            {subscription.plan && (
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Plan Details</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Plan Name</label>
                    <div className="flex items-center gap-2">
                      <p className="text-sm text-gray-900 font-medium">{subscription.plan.name}</p>
                      {subscription.plan.isPopular && (
                        <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                          Popular
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Price</label>
                    <p className="text-sm text-gray-900 font-medium">
                      {subscription.plan.currency} {subscription.plan.price.toLocaleString()}
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Billing Cycle</label>
                    <p className="text-sm text-gray-900 capitalize">{subscription.plan.billing}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Plan ID</label>
                    <p className="text-sm text-gray-900 font-mono">{subscription.plan.id}</p>
                  </div>
                </div>

                {subscription.plan.features && subscription.plan.features.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-3">Features</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {subscription.plan.features.map((feature, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                          <span className="text-sm text-gray-900">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Payment Details */}
            {subscription.payment && (
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Payment Information</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Payment ID</label>
                    <p className="text-sm text-gray-900 font-mono">{subscription.payment.id}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Amount</label>
                    <p className="text-sm text-gray-900 font-medium">
                      {subscription.payment.currency} {subscription.payment.amount.toLocaleString()}
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Payment Method</label>
                    <p className="text-sm text-gray-900 capitalize">{subscription.payment.method}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Status</label>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(subscription.payment.status)}`}>
                      {subscription.payment.status}
                    </span>
                  </div>
                  
                  {subscription.payment.transactionId && (
                    <div>
                      <label className="block text-sm font-medium text-gray-600 mb-1">Transaction ID</label>
                      <p className="text-sm text-gray-900 font-mono">{subscription.payment.transactionId}</p>
                    </div>
                  )}
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Payment Date</label>
                    <p className="text-sm text-gray-900">{subscription.payment.createdAt.toLocaleDateString()}</p>
                  </div>
                </div>

                <div className="mt-6 pt-6 border-t border-gray-200">
                  <Link
                    href={`/admin/payments/${subscription.payment.id}`}
                    className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 text-sm font-medium"
                  >
                    <DollarSign className="h-4 w-4" />
                    View Full Payment Details
                  </Link>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            
            {/* Admin Information */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Admin Information</h3>
              
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
                    <User className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{subscription.admin.name}</p>
                    <p className="text-xs text-gray-500">Administrator</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <p className="text-sm text-gray-900">{subscription.admin.email}</p>
                </div>
                
                {subscription.admin.phoneNumber && (
                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <p className="text-sm text-gray-900">{subscription.admin.phoneNumber}</p>
                  </div>
                )}
              </div>

              <div className="mt-6 pt-6 border-t border-gray-200">
                <Link
                  href={`/admin/users/${subscription.admin.id}`}
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                >
                  View Admin Profile
                </Link>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              
              <div className="space-y-3">
                <Link
                  href={`/admin/payments?subscription=${subscription.id}`}
                  className="w-full flex items-center justify-center gap-2 bg-blue-50 text-blue-700 py-2 px-3 rounded-md hover:bg-blue-100 transition-colors text-sm font-medium"
                >
                  <DollarSign className="h-4 w-4" />
                  View All Payments
                </Link>
                
                <Link
                  href={`/admin/subscriptions/${subscription.id}/edit`}
                  className="w-full flex items-center justify-center gap-2 bg-gray-50 text-gray-700 py-2 px-3 rounded-md hover:bg-gray-100 transition-colors text-sm font-medium"
                >
                  <Calendar className="h-4 w-4" />
                  Edit Subscription
                </Link>
                
                <button className="w-full flex items-center justify-center gap-2 bg-red-50 text-red-700 py-2 px-3 rounded-md hover:bg-red-100 transition-colors text-sm font-medium">
                  <XCircle className="h-4 w-4" />
                  Cancel Subscription
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
