import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Only show this in development mode
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Debug endpoint not available in production' },
        { status: 403 }
      );
    }

    const envCheck = {
      NODE_ENV: process.env.NODE_ENV,
      NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL,
      FLUTTERWAVE_PUBLIC_KEY: process.env.FLUTTERWAVE_PUBLIC_KEY ? 
        `${process.env.FLUTTERWAVE_PUBLIC_KEY.substring(0, 20)}...` : 'NOT SET',
      FLUTTERWAVE_SECRET_KEY: process.env.FLUTTERWAVE_SECRET_KEY ? 
        `${process.env.FLUTTERWAVE_SECRET_KEY.substring(0, 20)}...` : 'NOT SET',
      EMAIL_USER: process.env.EMAIL_USER ? 'SET' : 'NOT SET',
      EMAIL_PASS: process.env.EMAIL_PASS ? 'SET' : 'NOT SET',
      DATABASE_URL: process.env.DATABASE_URL ? 'SET' : 'NOT SET',
    };

    const issues = [];

    // Check for common issues
    if (!process.env.NEXT_PUBLIC_BASE_URL) {
      issues.push('NEXT_PUBLIC_BASE_URL is not set - this will cause redirect URL issues');
    }

    if (!process.env.FLUTTERWAVE_PUBLIC_KEY || !process.env.FLUTTERWAVE_SECRET_KEY) {
      issues.push('Flutterwave API keys are not set - payments will fail');
    }

    if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
      issues.push('Email configuration is missing - receipt emails will fail');
    }

    if (!process.env.DATABASE_URL) {
      issues.push('Database URL is not set - application will not work');
    }

    // Check if base URL has trailing slash (common issue)
    if (process.env.NEXT_PUBLIC_BASE_URL?.endsWith('/')) {
      issues.push('NEXT_PUBLIC_BASE_URL should not have a trailing slash');
    }

    return NextResponse.json({
      status: 'debug',
      environment: envCheck,
      issues: issues.length > 0 ? issues : ['No issues detected'],
      recommendations: [
        'Make sure all environment variables are set in your .env file',
        'Restart your development server after changing environment variables',
        'Use the exact format shown in .env.example',
        'For Flutterwave, use TEST keys for development and LIVE keys for production'
      ]
    });
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Failed to check environment', details: error.message },
      { status: 500 }
    );
  }
}
