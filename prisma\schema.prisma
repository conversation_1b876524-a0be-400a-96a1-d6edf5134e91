generator client {
  provider = "prisma-client-js"
  output   = "../app/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Univ{
id String @id @default(cuid())
name String
domain String @unique
students Student[]
admin     Admin  @relation(fields: [adminId], references: [id])
adminId   String   @unique
instructors Instructor[]
location String
verified String @default("Pending")
// Additional fields for comprehensive university data
logoUrl String?
address String?
city String?
state String?
country String?
zipCode String?
contactEmail String?
contactPhone String?
website String?
universityType String @default("public")
establishedYear Int?
studentCapacity Int?
description String?
allowSelfRegistration Boolean @default(false)
requireEmailVerification Boolean @default(true)
enableMultipleCampuses Boolean @default(false)
departments Department[]
instructorTokens InstructorToken[]
createdAt DateTime @default(now())
updatedAt DateTime @updatedAt
}


model Instructor{
  id String @id @default(cuid())
  name String
  email String @unique
  password String
  univ Univ[]
  students Student[] @relation("InstructorToStudent")
}



model Admin {
  id            String         @id @default(cuid())
  name          String
  email         String         @unique
  password      String
  phoneNumber   String
  univ          Univ?          @relation
  subscriptions Subscription[] @relation("UserSubscriptions")
  payments      Payment[]      @relation("UserPayments")
}


model Student{
id String @id @default(cuid())
name String
email String @unique
univId String @unique
password String
univ Univ @relation(fields: [univId],references: [id])
instructor   Instructor[] @relation("InstructorToStudent")
}


model TokenPayment{
  email String @unique
  payementToken String @unique 
}


model Plan {
  id            String         @id @default(cuid())
  name          String         @unique
  price         Float
  currency      String         @default("XAF")
  billing       String         // "monthly" or "yearly"
  features      String[]
  isPopular     Boolean        @default(false)
  savings       Float?
  isActive      Boolean        @default(true)
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  subscriptions Subscription[]
  payments      Payment[]
}

model Subscription {
  id                 String   @id @default(cuid())
  adminId            String
  planId             String?
  paymentId          String? @unique
  status             String   @default("unactive")
  activeDate         String   
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  payment Payment?   @relation(fields:[paymentId],references:[id],onDelete: Restrict)
  admin Admin @relation("UserSubscriptions", fields: [adminId], references: [id], onDelete: Cascade)
  plan  Plan?  @relation(fields: [planId], references: [id], onDelete: Restrict)

  @@unique([adminId, planId,paymentId])
}



model Payment {
  id              String   @id @default(cuid())
  adminId         String?
  planId          String?
  email           String
  amount          Float
  currency        String   @default("XAF")
  method          String
  status          String   @default("pending")
  transactionId   String?  @unique
  gatewayResponse Json?
  token           String   @unique
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  subscription   Subscription?
  admin Admin? @relation("UserPayments", fields: [adminId], references: [id], onDelete: SetNull)
  plan  Plan?  @relation(fields: [planId], references: [id], onDelete: SetNull)
}



model RenewalReminder {
  id             String   @id @default(cuid())
  adminId         String
  subscriptionId String
  reminderType   String   // "7_days", "3_days", "1_day", "expired"
  sentAt         DateTime @default(now())
  emailSent      Boolean  @default(false)
  smsSent        Boolean  @default(false)
  createdAt      DateTime @default(now())
}

model Coupon {
  id        String    @id @default(uuid())
  code      String    @unique
  discount  Float 
  type      String    @default("percentage") 
  expiresAt DateTime?
  isActive  Boolean   @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}


model Tokens {
  id String @unique @id @default(cuid())
  token String
}

model Department {
  id String @id @default(cuid())
  name String
  description String?
  code String // e.g., "CS", "MATH", "PHYS"
  headOfDepartment String?
  email String?
  phone String?
  location String?
  establishedYear Int?
  isActive Boolean @default(true)

  // Relations
  univId String
  univ Univ @relation(fields: [univId], references: [id], onDelete: Cascade)
  courses Course[]
  instructorTokens InstructorToken[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([univId, code])
}

model Course {
  id String @id @default(cuid())
  name String
  code String // e.g., "CS101", "MATH201"
  description String?
  credits Int @default(3)
  semester String? // "Fall", "Spring", "Summer"
  year Int?
  isActive Boolean @default(true)

  // Relations
  departmentId String
  department Department @relation(fields: [departmentId], references: [id], onDelete: Cascade)
  instructorTokens InstructorToken[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([departmentId, code])
}

model InstructorToken {
  id String @id @default(cuid())
  code String @unique // Generated token code

  // Relations
  univId String
  univ Univ @relation(fields: [univId], references: [id], onDelete: Cascade)

  departmentId String
  department Department @relation(fields: [departmentId], references: [id], onDelete: Cascade)

  courseId String?
  course Course? @relation(fields: [courseId], references: [id], onDelete: SetNull)

  // Token details
  maxUsage Int @default(50)
  usageCount Int @default(0)
  expirationDate DateTime
  status String @default("active") // "active", "expired", "revoked"

  // Metadata
  createdBy String // Admin ID or name
  notes String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}