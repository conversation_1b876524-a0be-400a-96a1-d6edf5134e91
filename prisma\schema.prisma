generator client {
  provider = "prisma-client-js"
  output   = "../app/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  activityLogs ActivityLog[]
}

model Univ{
id String @id @default(cuid())
name String
domain String @unique
students Student[]
admin     Admin  @relation(fields: [adminId], references: [id])
adminId   String   @unique
instructors Instructor[]
location String
verified String @default("Pending")
// Additional fields for comprehensive university data
logoUrl String?
address String?
city String?
state String?
country String?
zipCode String?
contactEmail String?
contactPhone String?
website String?
universityType String @default("public")
establishedYear Int?
studentCapacity Int?
description String?
allowSelfRegistration Boolean @default(false)
requireEmailVerification Boolean @default(true)
enableMultipleCampuses Boolean @default(false)
departments Department[]
instructorTokens InstructorToken[]
questions Question[]
exams Exam[]
createdAt DateTime @default(now())
updatedAt DateTime @updatedAt
}


model Instructor{
  id String @id @default(cuid())
  name String
  email String @unique
  password String
  univ Univ[]
  students Student[] @relation("InstructorToStudent")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}



model Admin {
  id            String         @id @default(cuid())
  name          String
  email         String         @unique
  password      String
  phoneNumber   String
  univ          Univ?          @relation
  subscriptions Subscription[] @relation("UserSubscriptions")
  payments      Payment[]      @relation("UserPayments")
}


model Student{
id String @id @default(cuid())
name String
email String @unique
univId String @unique
password String
univ Univ @relation(fields: [univId],references: [id])
instructor   Instructor[] @relation("InstructorToStudent")

// Relations
examResults ExamResult[]

createdAt DateTime @default(now())
updatedAt DateTime @updatedAt
}


model TokenPayment{
  email String @unique
  payementToken String @unique 
}


model Plan {
  id            String         @id @default(cuid())
  name          String         @unique
  price         Float
  currency      String         @default("XAF")
  billing       String         // "monthly" or "yearly"
  features      String[]
  isPopular     Boolean        @default(false)
  savings       Float?
  isActive      Boolean        @default(true)
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  subscriptions Subscription[]
  payments      Payment[]
}

model Subscription {
  id                 String   @id @default(cuid())
  adminId            String
  planId             String?
  paymentId          String? @unique
  status             String   @default("unactive")
  activeDate         String   
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  payment Payment?   @relation(fields:[paymentId],references:[id],onDelete: Restrict)
  admin Admin @relation("UserSubscriptions", fields: [adminId], references: [id], onDelete: Cascade)
  plan  Plan?  @relation(fields: [planId], references: [id], onDelete: Restrict)

  @@unique([adminId, planId,paymentId])
}



model Payment {
  id              String   @id @default(cuid())
  adminId         String?
  planId          String?
  email           String
  amount          Float
  currency        String   @default("XAF")
  method          String
  status          String   @default("pending")
  transactionId   String?  @unique
  gatewayResponse Json?
  token           String   @unique
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  subscription   Subscription?
  admin Admin? @relation("UserPayments", fields: [adminId], references: [id], onDelete: SetNull)
  plan  Plan?  @relation(fields: [planId], references: [id], onDelete: SetNull)
}



model RenewalReminder {
  id             String   @id @default(cuid())
  adminId         String
  subscriptionId String
  reminderType   String   // "7_days", "3_days", "1_day", "expired"
  sentAt         DateTime @default(now())
  emailSent      Boolean  @default(false)
  smsSent        Boolean  @default(false)
  createdAt      DateTime @default(now())
}

model Coupon {
  id        String    @id @default(uuid())
  code      String    @unique
  discount  Float 
  type      String    @default("percentage") 
  expiresAt DateTime?
  isActive  Boolean   @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}


model Tokens {
  id String @unique @id @default(cuid())
  token String
}

model Department {
  id String @id @default(cuid())
  name String
  description String?
  code String // e.g., "CS", "MATH", "PHYS"
  headOfDepartment String?
  email String?
  phone String?
  location String?
  establishedYear Int?
  isActive Boolean @default(true)

  // Relations
  univId String
  univ Univ @relation(fields: [univId], references: [id], onDelete: Cascade)
  courses Course[]
  instructorTokens InstructorToken[]
  questions Question[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([univId, code])
}

model Course {
  id String @id @default(cuid())
  name String
  code String // e.g., "CS101", "MATH201"
  description String?
  credits Int @default(3)
  semester String? // "Fall", "Spring", "Summer"
  year Int?
  isActive Boolean @default(true)

  // Relations
  departmentId String
  department Department @relation(fields: [departmentId], references: [id], onDelete: Cascade)
  instructorTokens InstructorToken[]
  questions Question[]
  exams Exam[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([departmentId, code])
}

model InstructorToken {
  id String @id @default(cuid())
  code String @unique // Generated token code

  // Relations
  univId String
  univ Univ @relation(fields: [univId], references: [id], onDelete: Cascade)

  departmentId String
  department Department @relation(fields: [departmentId], references: [id], onDelete: Cascade)

  courseId String?
  course Course? @relation(fields: [courseId], references: [id], onDelete: SetNull)

  // Token details
  maxUsage Int @default(50)
  usageCount Int @default(0)
  expirationDate DateTime
  status String @default("active") // "active", "expired", "revoked"

  // Metadata
  createdBy String // Admin ID or name
  notes String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model ActivityLog {
  id String @id @default(cuid())
  type String // Activity type (e.g., "user_registered", "payment_completed")
  action String // Human-readable description of the action
  userId String? // ID of the user who performed the action
  targetId String? // ID of the target entity (e.g., payment ID, subscription ID)
  targetType String? // Type of target entity (e.g., "payment", "subscription", "user")
  metadata Json? // Additional data related to the activity
  ipAddress String? // IP address of the user
  userAgent String? // User agent string from the request

  // Relations
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([type])
  @@index([userId])
  @@index([createdAt])
  @@index([targetType, targetId])
}

model Question {
  id String @id @default(cuid())
  title String
  text String // The question text
  type String // "multiple-choice", "essay", "true-false", "short-answer"
  subject String
  difficulty String // "Easy", "Medium", "Hard"
  tags String[] // Array of tags

  // Multiple choice specific fields
  options String[] // Array of options for multiple choice
  correctAnswer Int? // Index of correct answer for multiple choice

  // Essay specific fields
  wordLimit Int? // Word limit for essay questions

  // Relations
  courseId String?
  course Course? @relation(fields: [courseId], references: [id], onDelete: SetNull)

  departmentId String?
  department Department? @relation(fields: [departmentId], references: [id], onDelete: SetNull)

  univId String
  univ Univ @relation(fields: [univId], references: [id], onDelete: Cascade)

  createdBy String // Admin or instructor ID
  isActive Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([subject])
  @@index([difficulty])
  @@index([type])
  @@index([univId])
  @@index([courseId])
}

model Exam {
  id String @id @default(cuid())
  title String
  description String?

  // Relations
  courseId String
  course Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  univId String
  univ Univ @relation(fields: [univId], references: [id], onDelete: Cascade)

  // Exam configuration
  duration Int // Duration in minutes
  totalMarks Int @default(100)
  passingMarks Int @default(50)

  // Scheduling
  startTime DateTime
  endTime DateTime

  // Settings
  shuffleQuestions Boolean @default(false)
  showResults Boolean @default(true)
  allowRetake Boolean @default(false)
  isActive Boolean @default(true)

  createdBy String // Admin or instructor ID

  // Relations to results
  examResults ExamResult[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([courseId])
  @@index([univId])
  @@index([startTime])
}

model ExamResult {
  id String @id @default(cuid())

  // Relations
  examId String
  exam Exam @relation(fields: [examId], references: [id], onDelete: Cascade)

  studentId String
  student Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  // Results
  score Int
  totalMarks Int
  percentage Float
  grade String? // A, B, C, D, F

  // Timing
  startedAt DateTime
  submittedAt DateTime?
  duration Int? // Actual duration taken in minutes

  // Status
  status String @default("in_progress") // "in_progress", "submitted", "graded"

  // Answers (JSON format)
  answers Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([examId, studentId])
  @@index([studentId])
  @@index([examId])
}