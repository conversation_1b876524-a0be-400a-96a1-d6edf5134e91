import type { Metadata } from 'next'
import SideBar from './components/SideBar'

export const metadata: Metadata = {
  title: 'Instructor',
  description: 'Created with v0',
  generator: 'v0.dev',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body>
        <div className="flex min-h-screen bg-gray-50">
      
            <SideBar/>
            {children}
        </div>
        
        </body>
    </html>
  )
}
