import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // Check if the incoming request is for the /home path\
  if (request.nextUrl.pathname === '/home') {
    // Redirect to the desired destination, e.g., /dashboard
    return NextResponse.redirect(new URL('/', request.url));
  }

  // Allow the request to proceed if it's not for /home
  return NextResponse.next();
}

// Optional: Configure the middleware to match specific paths
export const config = {
    matcher: '/:path*',
  };