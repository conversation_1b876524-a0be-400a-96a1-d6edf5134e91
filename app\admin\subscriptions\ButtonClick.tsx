"use client"
import { Subscription } from '@/app/generated/prisma'
import { createToken } from '@/app/lib/token'
import { Calendar, ChevronRight } from 'lucide-react'
import { useRouter } from 'next/router'
import React from 'react'
import { toast } from 'sonner'

type Props = {
    subscription:Subscription
    user:{
        id:string,
        email:string
    }
}
const handleCreate = async ({subscriptionId,user}:{subscriptionId:string,user:{
    id:string,
    email:string
}}) => {
    const res = await fetch('/api/subscriptions/token',{
        method:'POST'
    })
    const router  = useRouter()
    router.push('/admin/create-university')
  }
const ButtonClick = ({subscription,user}: Props) => {
  return (
     <button
            onClick={() => handleCreate({subscriptionId:subscription.id,user})}
              className="flex items-center justify-center gap-2 bg-gray-50 text-gray-700 py-2 px-3 rounded-md hover:bg-gray-100 transition-colors text-sm font-medium group"
            >
              <Calendar className="h-4 w-4" />
              User this subscription
              <ChevronRight className="h-3 w-3 group-hover:translate-x-0.5 transition-transform" />
            </button>
  )
}

export default ButtonClick