"use client"
import clsx from 'clsx'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React, { useState } from 'react'
import  { LayoutDashboard,ClipboardList,Clock,CheckCircle,Award,User,LogOut,HelpCircle} from 'lucide-react'
const SideBar = () => {
    const pathName = usePathname().split('/')[4]
    const data = [
        {
          name: 'Dashboard',
          href: 'dashboard',
          icon: <LayoutDashboard/>
        },
        {
          name: 'Result',
          href: 'result',
          icon: <Award/>
        },
        {
          name: 'Instruction',
          href: 'instruction',
          icon: <ClipboardList/>
        },
        {
          name: 'Take Exam',
          href: 'take-exam',
          icon: <Clock/>
        },
        {
          name: 'Submitted Exams',
          href: 'submitted-exams',
          icon: <CheckCircle/>
        },
        {
          name: 'Profile',
          href: 'profile',
          icon: <User/>
        },
        {
          name: 'Help',
          href: 'help',
          icon: <HelpCircle/>
        },
    ]
    const [activeLink,setActiveLink] = useState(pathName == "" ? pathName : 'dashboard')
  return (
      <div className="w-64 bg-white border-r min-h-full border-gray-200 flex flex-col">
        {/* Logo */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">K</span>
            </div>
            <span className="font-semibold text-lg">King Fayssal</span>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex-1 p-4">
          <div className="space-y-6">
            {/* General Section */}
            <div>
              <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-3">GENERAL</h3>
              <nav className="space-y-1">
                {
                    data.map((item,i) => (
                        <Link key={i+item.name} href={item.href} onClick={() => setActiveLink(item.href)} className={clsx('flex items-center gap-3 px-3 py-2 text-sm rounded-lg',{
                             'bg-green-600 text-white':activeLink === item.href ,
                             'text-gray-600 hover:bg-green-50':activeLink !== item.href
                        })}><span className={clsx('text-lg',{
                            'text-white':activeLink == item.href
                        })}>{item.icon}</span>{item.name}</Link>
                    ) )
                }
                {/* <a
                  href="#"
                  className="flex items-center justify-between px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <span className="w-4 h-4">💬</span>
                    Message
                  </div>
                  <span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">8</span>
                </a> */}
              </nav>
            </div>

            

            
          </div>
        </div>

        
      </div>
  )
}

export default SideBar
