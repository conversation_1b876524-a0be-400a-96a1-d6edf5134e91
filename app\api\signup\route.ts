import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";
import bcrypt from "bcrypt";
import { createSession } from "@/app/lib/session";

export async function 
POST(request: Request) {
    const { name, email, password } = await request.json();
    try {
        const existingUser = await prisma.user.findUnique({
            where: {
                email,
            }
        });
        if (existingUser) {
            return NextResponse.json({
                message: "User already exists."
            },{
                status: 400
            }) 
        }
        const hashedPassword = await bcrypt.hash(password, 10); // In a real application, you should hash the password before storing it
      const res =  await prisma.user.create({
            data: {
                name,
                email,
                password:hashedPassword, // In a real application, make sure to hash the password before storing it
            }
        });
        const user = {id:res.id,email:res.email,role:''}
        await createSession(user)
        return NextResponse.json({
            message: "User created successfully."
        },{
            status: 200
        })
    } catch (error) {
        console.log(error)
        return NextResponse.json({
            message: "Something went wrong. Check your internet connection or try again later."
        },{
            status: 500
        }) 
    }
}