"use client"

import { useState } from "react"
import { Save, Mail, Users, SettingsIcon, CheckCircle } from "lucide-react"

export default function SystemSettings() {
  const [activeTab, setActiveTab] = useState("exam")
  const [settings, setSettings] = useState({
    exam: {
      defaultDuration: 120,
      antiCheatEnabled: true,
      maxLoginAttempts: 3,
      allowLateJoin: false,
      lateJoinWindow: 15,
      autoSubmit: true,
      warningTime: 10,
    },
    email: {
      tokenInviteSubject: "Invitation to ExamPro Platform",
      tokenInviteTemplate:
        "Dear Instructor,\n\nYou have been invited to join the ExamPro platform. Your access token is: {{TOKEN}}\n\nBest regards,\nExamPro Team",
      resetPasswordSubject: "Password Reset Request",
      resetPasswordTemplate:
        "Dear User,\n\nClick the link below to reset your password:\n{{RESET_LINK}}\n\nBest regards,\nExamPro Team",
      banNoticeSubject: "Account Suspended",
      banNoticeTemplate:
        "Dear User,\n\nYour account has been suspended due to policy violations.\n\nBest regards,\nExamPro Team",
    },
    behavior: {
      autoBlockThreshold: 5,
      inactivityTimeout: 30,
      suspiciousActivityAlert: true,
      multipleLoginBlock: true,
      ipChangeAlert: true,
      sessionTimeout: 480,
    },
  })

  const tabs = [
    { id: "exam", label: "Exam Configuration", icon: SettingsIcon },
    { id: "email", label: "Email Settings", icon: Mail },
    { id: "behavior", label: "User Behavior", icon: Users },
  ]

  const handleSettingChange = (category: string, key: string, value: any) => {
    setSettings((prev) => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [key]: value,
      },
    }))
  }

  const saveSettings = () => {
    // Here you would typically send the settings to your backend
    console.log("Saving settings:", settings)
    // Show success message
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="p-6">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">System Settings</h1>
          <p className="text-gray-600">Configure platform-wide settings and preferences</p>
        </div>

        <div className="flex flex-col lg:flex-row gap-6">
          {/* Sidebar Tabs */}
          <div className="lg:w-64">
            <div className="bg-white rounded-lg shadow-sm border p-2">
              <nav className="space-y-1">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      activeTab === tab.id
                        ? "bg-blue-50 text-blue-600"
                        : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                    }`}
                  >
                    <tab.icon className="h-5 w-5" />
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Settings Content */}
          <div className="flex-1">
            <div className="bg-white rounded-lg shadow-sm border">
              {/* Exam Configuration */}
              {activeTab === "exam" && (
                <div className="p-6">
                  <div className="mb-6">
                    <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                      <SettingsIcon className="h-5 w-5" />
                      Exam Configuration
                    </h2>
                    <p className="text-gray-600">Configure default exam settings and anti-cheat measures</p>
                  </div>

                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Default Exam Duration (minutes)
                        </label>
                        <input
                          type="number"
                          value={settings.exam.defaultDuration}
                          onChange={(e) =>
                            handleSettingChange("exam", "defaultDuration", Number.parseInt(e.target.value))
                          }
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Maximum Login Attempts</label>
                        <input
                          type="number"
                          value={settings.exam.maxLoginAttempts}
                          onChange={(e) =>
                            handleSettingChange("exam", "maxLoginAttempts", Number.parseInt(e.target.value))
                          }
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Late Join Window (minutes)
                        </label>
                        <input
                          type="number"
                          value={settings.exam.lateJoinWindow}
                          onChange={(e) =>
                            handleSettingChange("exam", "lateJoinWindow", Number.parseInt(e.target.value))
                          }
                          disabled={!settings.exam.allowLateJoin}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Warning Time Before Auto-Submit (minutes)
                        </label>
                        <input
                          type="number"
                          value={settings.exam.warningTime}
                          onChange={(e) => handleSettingChange("exam", "warningTime", Number.parseInt(e.target.value))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div>
                          <h3 className="text-sm font-medium text-gray-900">Anti-Cheat Detection</h3>
                          <p className="text-sm text-gray-500">Enable advanced anti-cheat monitoring</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.exam.antiCheatEnabled}
                            onChange={(e) => handleSettingChange("exam", "antiCheatEnabled", e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div>
                          <h3 className="text-sm font-medium text-gray-900">Allow Late Join</h3>
                          <p className="text-sm text-gray-500">Allow students to join exams after start time</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.exam.allowLateJoin}
                            onChange={(e) => handleSettingChange("exam", "allowLateJoin", e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div>
                          <h3 className="text-sm font-medium text-gray-900">Auto-Submit</h3>
                          <p className="text-sm text-gray-500">Automatically submit exams when time expires</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.exam.autoSubmit}
                            onChange={(e) => handleSettingChange("exam", "autoSubmit", e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Email Settings */}
              {activeTab === "email" && (
                <div className="p-6">
                  <div className="mb-6">
                    <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                      <Mail className="h-5 w-5" />
                      Email Settings
                    </h2>
                    <p className="text-gray-600">Customize email templates and notifications</p>
                  </div>

                  <div className="space-y-8">
                    {/* Token Invite Email */}
                    <div>
                      <h3 className="text-md font-medium text-gray-900 mb-4">Token Invitation Email</h3>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Subject Line</label>
                          <input
                            type="text"
                            value={settings.email.tokenInviteSubject}
                            onChange={(e) => handleSettingChange("email", "tokenInviteSubject", e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Email Template</label>
                          <textarea
                            rows={6}
                            value={settings.email.tokenInviteTemplate}
                            onChange={(e) => handleSettingChange("email", "tokenInviteTemplate", e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                          <p className="text-xs text-gray-500 mt-1">
                            Use {"{{TOKEN}}"} as placeholder for the access token
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Password Reset Email */}
                    <div>
                      <h3 className="text-md font-medium text-gray-900 mb-4">Password Reset Email</h3>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Subject Line</label>
                          <input
                            type="text"
                            value={settings.email.resetPasswordSubject}
                            onChange={(e) => handleSettingChange("email", "resetPasswordSubject", e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Email Template</label>
                          <textarea
                            rows={6}
                            value={settings.email.resetPasswordTemplate}
                            onChange={(e) => handleSettingChange("email", "resetPasswordTemplate", e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                          <p className="text-xs text-gray-500 mt-1">
                            Use {"{{RESET_LINK}}"} as placeholder for the reset link
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Ban Notice Email */}
                    <div>
                      <h3 className="text-md font-medium text-gray-900 mb-4">Account Suspension Notice</h3>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Subject Line</label>
                          <input
                            type="text"
                            value={settings.email.banNoticeSubject}
                            onChange={(e) => handleSettingChange("email", "banNoticeSubject", e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Email Template</label>
                          <textarea
                            rows={6}
                            value={settings.email.banNoticeTemplate}
                            onChange={(e) => handleSettingChange("email", "banNoticeTemplate", e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* User Behavior Settings */}
              {activeTab === "behavior" && (
                <div className="p-6">
                  <div className="mb-6">
                    <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                      <Users className="h-5 w-5" />
                      User Behavior Settings
                    </h2>
                    <p className="text-gray-600">Configure automatic actions and security thresholds</p>
                  </div>

                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Auto-Block Threshold (failed attempts)
                        </label>
                        <input
                          type="number"
                          value={settings.behavior.autoBlockThreshold}
                          onChange={(e) =>
                            handleSettingChange("behavior", "autoBlockThreshold", Number.parseInt(e.target.value))
                          }
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Inactivity Timeout (minutes)
                        </label>
                        <input
                          type="number"
                          value={settings.behavior.inactivityTimeout}
                          onChange={(e) =>
                            handleSettingChange("behavior", "inactivityTimeout", Number.parseInt(e.target.value))
                          }
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>

                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Session Timeout (minutes)
                        </label>
                        <input
                          type="number"
                          value={settings.behavior.sessionTimeout}
                          onChange={(e) =>
                            handleSettingChange("behavior", "sessionTimeout", Number.parseInt(e.target.value))
                          }
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div>
                          <h3 className="text-sm font-medium text-gray-900">Suspicious Activity Alerts</h3>
                          <p className="text-sm text-gray-500">Send alerts when suspicious activity is detected</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.behavior.suspiciousActivityAlert}
                            onChange={(e) =>
                              handleSettingChange("behavior", "suspiciousActivityAlert", e.target.checked)
                            }
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div>
                          <h3 className="text-sm font-medium text-gray-900">Multiple Login Block</h3>
                          <p className="text-sm text-gray-500">
                            Automatically block accounts with multiple simultaneous logins
                          </p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.behavior.multipleLoginBlock}
                            onChange={(e) => handleSettingChange("behavior", "multipleLoginBlock", e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div>
                          <h3 className="text-sm font-medium text-gray-900">IP Change Alerts</h3>
                          <p className="text-sm text-gray-500">Alert when users change IP addresses during exams</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.behavior.ipChangeAlert}
                            onChange={(e) => handleSettingChange("behavior", "ipChangeAlert", e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Save Button */}
              <div className="border-t px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    Settings are automatically saved
                  </div>
                  <button
                    onClick={saveSettings}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    <Save className="h-4 w-4" />
                    Save Changes
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
