import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface ActivityLogData {
  type: string;
  action: string;
  userId?: string;
  targetId?: string;
  targetType?: string;
  metadata?: any;
  ipAddress?: string;
  userAgent?: string;
}

export class ActivityService {
  /**
   * Log an activity to the database
   */
  static async logActivity(data: ActivityLogData): Promise<void> {
    try {
      await prisma.activityLog.create({
        data: {
          type: data.type,
          action: data.action,
          userId: data.userId || null,
          targetId: data.targetId || null,
          targetType: data.targetType || null,
          metadata: data.metadata || null,
          ipAddress: data.ipAddress || null,
          userAgent: data.userAgent || null,
        },
      });
    } catch (error) {
      console.error('Failed to log activity:', error);
      // Don't throw error to avoid breaking the main flow
    }
  }

  /**
   * Get recent activities for admin dashboard
   */
  static async getRecentActivities(limit: number = 50) {
    try {
      const activities = await prisma.activityLog.findMany({
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
      });

      return activities.map((activity:any) => ({
        id: activity.id,
        type: activity.type,
        action: activity.action,
        user: activity.user?.name || activity.user?.email || 'System',
        userId: activity.userId,
        targetId: activity.targetId,
        targetType: activity.targetType,
        metadata: activity.metadata,
        time: this.formatTimeAgo(activity.createdAt),
        timestamp: activity.createdAt,
      }));
    } catch (error) {
      console.error('Failed to fetch recent activities:', error);
      return [];
    }
  }

  /**
   * Get activities by type
   */
  static async getActivitiesByType(type: string, limit: number = 20) {
    try {
      const activities = await prisma.activityLog.findMany({
        where: { type },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: limit,
      });

      return activities.map((activity:any) => ({
        id: activity.id,
        type: activity.type,
        action: activity.action,
        user: activity.user?.name || activity.user?.email || 'System',
        userId: activity.userId,
        targetId: activity.targetId,
        targetType: activity.targetType,
        metadata: activity.metadata,
        time: this.formatTimeAgo(activity.createdAt),
        timestamp: activity.createdAt,
      }));
    } catch (error) {
      console.error('Failed to fetch activities by type:', error);
      return [];
    }
  }

  /**
   * Get activity statistics
   */
  static async getActivityStats() {
    try {
      const [
        totalActivities,
        todayActivities,
        userRegistrations,
        paymentActivities,
        subscriptionActivities,
      ] = await Promise.all([
        prisma.activityLog.count(),
        prisma.activityLog.count({
          where: {
            createdAt: {
              gte: new Date(new Date().setHours(0, 0, 0, 0)),
            },
          },
        }),
        prisma.activityLog.count({
          where: { type: 'user_registered' },
        }),
        prisma.activityLog.count({
          where: { type: { in: ['payment_completed', 'payment_failed'] } },
        }),
        prisma.activityLog.count({
          where: { type: { in: ['subscription_created', 'subscription_cancelled'] } },
        }),
      ]);

      return {
        totalActivities,
        todayActivities,
        userRegistrations,
        paymentActivities,
        subscriptionActivities,
      };
    } catch (error) {
      console.error('Failed to fetch activity stats:', error);
      return {
        totalActivities: 0,
        todayActivities: 0,
        userRegistrations: 0,
        paymentActivities: 0,
        subscriptionActivities: 0,
      };
    }
  }

  /**
   * Format time ago string
   */
  private static formatTimeAgo(date: Date): string {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return `${diffInSeconds} seconds ago`;
    }

    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    }

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    }

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) {
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    }

    const diffInWeeks = Math.floor(diffInDays / 7);
    if (diffInWeeks < 4) {
      return `${diffInWeeks} week${diffInWeeks > 1 ? 's' : ''} ago`;
    }

    const diffInMonths = Math.floor(diffInDays / 30);
    return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`;
  }

  /**
   * Predefined activity types and their human-readable actions
   */
  static readonly ACTIVITY_TYPES = {
    // User activities
    USER_REGISTERED: 'user_registered',
    USER_LOGIN: 'user_login',
    USER_LOGOUT: 'user_logout',
    USER_PROFILE_UPDATED: 'user_profile_updated',
    USER_PASSWORD_CHANGED: 'user_password_changed',
    
    // Payment activities
    PAYMENT_INITIATED: 'payment_initiated',
    PAYMENT_COMPLETED: 'payment_completed',
    PAYMENT_FAILED: 'payment_failed',
    PAYMENT_CANCELLED: 'payment_cancelled',
    
    // Subscription activities
    SUBSCRIPTION_CREATED: 'subscription_created',
    SUBSCRIPTION_RENEWED: 'subscription_renewed',
    SUBSCRIPTION_CANCELLED: 'subscription_cancelled',
    SUBSCRIPTION_EXPIRED: 'subscription_expired',
    
    // Admin activities
    ADMIN_LOGIN: 'admin_login',
    ADMIN_ACTION: 'admin_action',
    
    // System activities
    SYSTEM_MAINTENANCE: 'system_maintenance',
    SYSTEM_ERROR: 'system_error',
  } as const;

  /**
   * Helper methods for common activity logging
   */
  static async logUserRegistration(userId: string, email: string, ipAddress?: string, userAgent?: string) {
    await this.logActivity({
      type: this.ACTIVITY_TYPES.USER_REGISTERED,
      action: `New user registered: ${email}`,
      userId,
      targetId: userId,
      targetType: 'user',
      metadata: { email },
      ipAddress,
      userAgent,
    });
  }

  static async logPaymentCompleted(paymentId: string, userId: string, amount: number, currency: string, ipAddress?: string) {
    await this.logActivity({
      type: this.ACTIVITY_TYPES.PAYMENT_COMPLETED,
      action: `Payment completed: ${amount} ${currency}`,
      userId,
      targetId: paymentId,
      targetType: 'payment',
      metadata: { amount, currency },
      ipAddress,
    });
  }

  static async logSubscriptionCreated(subscriptionId: string, userId: string, planName: string) {
    await this.logActivity({
      type: this.ACTIVITY_TYPES.SUBSCRIPTION_CREATED,
      action: `New subscription created: ${planName}`,
      userId,
      targetId: subscriptionId,
      targetType: 'subscription',
      metadata: { planName },
    });
  }
}
