import React from 'react'
import { verifySession } from '../lib/session'
import { redirect } from 'next/navigation'
import PaymentPage from './Form'
import prisma from '../lib/prisma'

const page = async () => {
  const user = await verifySession()
  if(!user || user.role !== 'admin') redirect('/admin/login')
    const admin = await prisma.admin.findFirst({where:{id:user.id}})
    if(!admin) redirect('/admin/login')
    const D_prices = await prisma.plan.findMany({select:{price:true,billing:true},where:{isActive:true}})
    const prices = D_prices.reduce((acc,curr) => {
        acc[curr.billing as 'monthly' | 'yearly'] = curr.price
        return acc
    },{} as {monthly:number,yearly:number})
  return (
    <PaymentPage p_email={user.email} prices={prices}/>
  )
}

export default page