'use client'

import React from 'react'
import useSideBarStore from '../sidebar.store'
import { useShallow } from 'zustand/shallow'

const SideBarOverlay = () => {
    const {sidebarOpen,setSidebarOpen} = useSideBarStore(useShallow(state => ({
        sidebarOpen: state.open,
        setSidebarOpen: state.setOpen
    })))
  return (
    <>
    {sidebarOpen && (
        <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
        onClick={() => setSidebarOpen(false)}
        ></div>
    )}
    </>
  )
}
export default SideBarOverlay
