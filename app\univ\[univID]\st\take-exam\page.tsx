// import React, { useState, useEffect, useRef } from 'react';
// import { useParams, useNavigate } from 'react-router-dom';
// import Webcam from 'react-webcam';
// import examData from '../data/mockExamData';
// import { AiOutlineEyeInvisible, AiOutlineEye } from 'react-icons/ai';

// interface Question {
//   id: number;
//   question: string;
//   options: string[];
//   answer: string;
// }

// interface Exam {
//   id: string;
//   title: string;
//   description: string;
//   duration: number;
//   questions: Question[];
// }

// const TakeExam: React.FC = () => {
//   const { examId } = useParams<{ examId: string }>();
//   const navigate = useNavigate();
//   const webcamRef = useRef<Webcam | null>(null);

//   const [exam, setExam] = useState<Exam | null>(null);
//   const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
//   const [answers, setAnswers] = useState<{ [key: number]: string }>({});
//   const [timer, setTimer] = useState(0);
//   const [recording, setRecording] = useState(false);
//   const [videoURL, setVideoURL] = useState('');
//   const [micAccess, setMicAccess] = useState(false);
//   const [webcamAccess, setWebcamAccess] = useState(false);
//   const [showVideoFeed, setShowVideoFeed] = useState(true);

//   const mediaRecorderRef = useRef<MediaRecorder | null>(null);
//   const recordedChunksRef = useRef<Blob[]>([]);

//   useEffect(() => {
//     const foundExam = examData.find((e:any) => e.id === examId);
//     if (foundExam) {
//       setExam(foundExam);
//       setTimer(foundExam.duration * 60);
//     }
//   }, [examId]);

//   useEffect(() => {
//     if (timer <= 0) {
//       handleSubmit();
//       return;
//     }
//     const countdown = setInterval(() => {
//       setTimer((prev) => prev - 1);
//     }, 1000);
//     return () => clearInterval(countdown);
//   }, [timer]);

//   useEffect(() => {
//     const getMedia = async () => {
//       try {
//         const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
//         setWebcamAccess(true);
//         setMicAccess(true);
//         startRecording(stream);
//       } catch (err) {
//         alert('Please allow camera and microphone access to start the exam.');
//       }
//     };
//     getMedia();
//   }, []);

//   const startRecording = (stream: MediaStream) => {
//     recordedChunksRef.current = [];
//     const mediaRecorder = new MediaRecorder(stream);
//     mediaRecorderRef.current = mediaRecorder;

//     mediaRecorder.ondataavailable = (e) => {
//       if (e.data.size > 0) {
//         recordedChunksRef.current.push(e.data);
//       }
//     };

//     mediaRecorder.onstop = () => {
//       const blob = new Blob(recordedChunksRef.current, { type: 'video/webm' });
//       const url = URL.createObjectURL(blob);
//       setVideoURL(url);
//     };

//     mediaRecorder.start();
//     setRecording(true);
//   };

//   const stopRecording = () => {
//     mediaRecorderRef.current?.stop();
//     setRecording(false);
//   };

//   const handleOptionSelect = (option: string) => {
//     if (!exam) return;
//     setAnswers((prev) => ({
//       ...prev,
//       [exam.questions[currentQuestionIndex].id]: option,
//     }));
//   };

//   const handleNext = () => {
//     if (exam && currentQuestionIndex < exam.questions.length - 1) {
//       setCurrentQuestionIndex((prev) => prev + 1);
//     }
//   };

//   const handlePrevious = () => {
//     if (currentQuestionIndex > 0) {
//       setCurrentQuestionIndex((prev) => prev - 1);
//     }
//   };

//   const handleSubmit = () => {
//     stopRecording();
//     navigate(`/exam/submitted`, {
//       state: {
//         examId,
//         answers,
//         recordedVideo: videoURL,
//         score: calculateScore(),
//       },
//     });
//   };

//   const calculateScore = () => {
//     if (!exam) return 0;
//     let score = 0;
//     exam.questions.forEach((q) => {
//       if (answers[q.id] === q.answer) {
//         score++;
//       }
//     });
//     return score;
//   };

//   const toggleVideoFeed = () => {
//     setShowVideoFeed((prev) => !prev);
//   };

//   if (!exam) return <p className="text-center mt-10 text-lg">Loading exam...</p>;

//   const currentQuestion = exam.questions[currentQuestionIndex];

//   return (
//     <div className="max-w-4xl mx-auto p-6">
//       <header className="flex justify-between items-center bg-blue-100 p-4 rounded-lg mb-6">
//         <h2 className="text-xl font-semibold text-blue-800">{exam.title}</h2>
//         <div className="text-red-600 font-bold">
//           Time Left: {Math.floor(timer / 60)}:{String(timer % 60).padStart(2, '0')}
//         </div>
//       </header>

//       <section className="mb-6">
//         <div className="mb-4">
//           <h3 className="text-lg font-semibold">Question {currentQuestionIndex + 1}</h3>
//           <p className="text-gray-800">{currentQuestion.question}</p>
//         </div>
//         <div className="flex flex-col gap-2">
//           {currentQuestion.options.map((opt, idx) => (
//             <label
//               key={idx}
//               className={`p-3 border rounded-lg cursor-pointer transition ${
//                 answers[currentQuestion.id] === opt
//                   ? 'bg-blue-200 border-blue-600'
//                   : 'hover:bg-gray-100 border-gray-300'
//               }`}
//             >
//               <input
//                 type="radio"
//                 name={`question-${currentQuestion.id}`}
//                 value={opt}
//                 checked={answers[currentQuestion.id] === opt}
//                 onChange={() => handleOptionSelect(opt)}
//                 className="mr-2"
//               />
//               {opt}
//             </label>
//           ))}
//         </div>
//       </section>

//       <section className="flex justify-center gap-4 mb-6">
//         <button
//           onClick={handlePrevious}
//           disabled={currentQuestionIndex === 0}
//           className="px-4 py-2 bg-gray-300 rounded disabled:opacity-50"
//         >
//           Previous
//         </button>
//         <button
//           onClick={handleNext}
//           disabled={currentQuestionIndex === exam.questions.length - 1}
//           className="px-4 py-2 bg-gray-300 rounded disabled:opacity-50"
//         >
//           Next
//         </button>
//         <button
//           onClick={handleSubmit}
//           className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
//         >
//           Submit Exam
//         </button>
//       </section>

//       <section className="border p-4 rounded-lg mt-4">
//         <div className="flex justify-between items-center mb-2">
//           <span className="font-medium text-gray-800">AI Proctoring Active</span>
//           <button onClick={toggleVideoFeed} className="text-gray-600 text-xl">
//             {showVideoFeed ? <AiOutlineEyeInvisible /> : <AiOutlineEye />}
//           </button>
//         </div>

//         {webcamAccess && showVideoFeed && (
//           <Webcam
//             ref={webcamRef}
//             audio={false}
//             className="w-full h-60 rounded border"
//             mirrored
//           />
//         )}

//         {!webcamAccess && (
//           <p className="text-red-600 font-bold mt-2">Webcam access not granted</p>
//         )}
//         {!micAccess && (
//           <p className="text-red-600 font-bold mt-1">Microphone access not granted</p>
//         )}
//       </section>
//     </div>
//   );
// };

// export default TakeExam;

import React from 'react'

type Props = {}

const page = (props: Props) => {
  return (
    <div>page</div>
  )
}

export default page