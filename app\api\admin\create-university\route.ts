import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';
import prisma from '@/app/lib/prisma';
import { verifySession } from '@/app/lib/session';

export async function POST(request: NextRequest) {
  try {
    // Verify admin session
    const user = await verifySession();
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    
    // Extract form fields
    const name = formData.get('name') as string;
    const domain = formData.get('domain') as string;
    const logo = formData.get('logo') as File | null;
    
    // Location fields
    const address = formData.get('address') as string;
    const city = formData.get('city') as string;
    const state = formData.get('state') as string;
    const country = formData.get('country') as string;
    const zipCode = formData.get('zipCode') as string;
    
    // Contact fields
    const contactEmail = formData.get('contactEmail') as string;
    const contactPhone = formData.get('contactPhone') as string;
    const website = formData.get('website') as string;
    
    // Details fields
    const universityType = formData.get('universityType') as string;
    const establishedYear = formData.get('establishedYear') as string;
    const studentCapacity = formData.get('studentCapacity') as string;
    const description = formData.get('description') as string;
    
    // Settings fields
    const allowSelfRegistration = formData.get('allowSelfRegistration') === 'true';
    const requireEmailVerification = formData.get('requireEmailVerification') === 'true';
    const enableMultipleCampuses = formData.get('enableMultipleCampuses') === 'true';

    // Validate required fields
    if (!name || !domain) {
      return NextResponse.json(
        { success: false, error: 'Name and domain are required' },
        { status: 400 }
      );
    }

    // Check if domain already exists
    const existingUniv = await prisma.univ.findUnique({
      where: { domain }
    });

    if (existingUniv) {
      return NextResponse.json(
        { success: false, error: 'Domain already exists' },
        { status: 400 }
      );
    }

    let logoUrl: string | null = null;

    // Handle logo upload if provided
    if (logo && logo.size > 0) {
      try {
        // Create assets directory if it doesn't exist
        const assetsDir = join(process.cwd(), 'public', 'assets', 'university-logos');
        if (!existsSync(assetsDir)) {
          await mkdir(assetsDir, { recursive: true });
        }

        // Generate unique filename
        const timestamp = Date.now();
        const fileExtension = logo.name.split('.').pop();
        const fileName = `${domain}-${timestamp}.${fileExtension}`;
        const filePath = join(assetsDir, fileName);

        // Convert file to buffer and save
        const bytes = await logo.arrayBuffer();
        const buffer = Buffer.from(bytes);
        await writeFile(filePath, buffer);

        // Create HTTP URL for the logo
        logoUrl = `/assets/university-logos/${fileName}`;
      } catch (error) {
        console.error('Logo upload failed:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to upload logo' },
          { status: 500 }
        );
      }
    }

    // Create university in database
    const university = await prisma.univ.create({
      data: {
        name,
        domain,
        adminId: user.id,
        location: `${city}, ${state}, ${country}`, // Keep existing location field for compatibility
        logoUrl,
        address,
        city,
        state,
        country,
        zipCode,
        contactEmail,
        contactPhone,
        website,
        universityType: universityType || 'public',
        establishedYear: establishedYear ? parseInt(establishedYear) : null,
        studentCapacity: studentCapacity ? parseInt(studentCapacity) : null,
        description,
        allowSelfRegistration,
        requireEmailVerification,
        enableMultipleCampuses,
        verified: 'Pending'
      }
    });

    return NextResponse.json({
      success: true,
      university: {
        id: university.id,
        name: university.name,
        domain: university.domain,
        logoUrl: university.logoUrl,
        verified: university.verified
      }
    });

  } catch (error: any) {
    console.error('University creation failed:', error);
    
    // Handle specific Prisma errors
    if (error.code === 'P2002') {
      return NextResponse.json(
        { success: false, error: 'Domain already exists' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create university' },
      { status: 500 }
    );
  }
}

// GET method to retrieve universities (optional)
export async function GET(request: NextRequest) {
  try {
    const user = await verifySession();
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const universities = await prisma.univ.findMany({
      where: { adminId: user.id },
      select: {
        id: true,
        name: true,
        domain: true,
        location: true,
        logoUrl: true,
        verified: true,
        createdAt: true,
        students: {
          select: { id: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    const enrichedUniversities = universities.map(univ => ({
      ...univ,
      studentCount: univ.students.length
    }));

    return NextResponse.json({
      success: true,
      universities: enrichedUniversities
    });

  } catch (error) {
    console.error('Failed to fetch universities:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch universities' },
      { status: 500 }
    );
  }
}
