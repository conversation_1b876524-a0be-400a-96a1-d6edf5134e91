import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/app/lib/prisma';
import { verifySession } from '@/app/lib/session';

// Generate a unique token code
function generateTokenCode(departmentCode: string, courseCode?: string): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  
  if (courseCode) {
    return `${departmentCode}-${courseCode}-${timestamp}-${random}`.toUpperCase();
  } else {
    return `${departmentCode}-ALL-${timestamp}-${random}`.toUpperCase();
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { univId: string } }
) {
  try {
    // Verify admin session
    const user = await verifySession();
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { univId } = params;

    // Verify the university belongs to this admin
    const university = await prisma.univ.findFirst({
      where: {
        id: univId,
        adminId: user.id
      }
    });

    if (!university) {
      return NextResponse.json(
        { success: false, error: 'University not found or access denied' },
        { status: 404 }
      );
    }

    const body = await request.json();
    const {
      departmentId,
      courseId,
      maxUsage,
      expirationDate,
      notes
    } = body;

    // Validate required fields
    if (!departmentId || !maxUsage || !expirationDate) {
      return NextResponse.json(
        { success: false, error: 'Department, max usage, and expiration date are required' },
        { status: 400 }
      );
    }

    // Verify department belongs to this university
    const department = await prisma.department.findFirst({
      where: {
        id: departmentId,
        univId: univId,
        isActive: true
      }
    });

    if (!department) {
      return NextResponse.json(
        { success: false, error: 'Department not found' },
        { status: 404 }
      );
    }

    // If courseId is provided, verify it belongs to the department
    let course = null;
    if (courseId) {
      course = await prisma.course.findFirst({
        where: {
          id: courseId,
          departmentId: departmentId,
          isActive: true
        }
      });

      if (!course) {
        return NextResponse.json(
          { success: false, error: 'Course not found in the specified department' },
          { status: 404 }
        );
      }
    }

    // Generate unique token code
    const tokenCode = generateTokenCode(department.code, course?.code);

    // Ensure token code is unique
    const existingToken = await prisma.instructorToken.findFirst({
      where: { code: tokenCode }
    });

    if (existingToken) {
      // If collision (very rare), try again with different random
      const newTokenCode = generateTokenCode(department.code, course?.code);
      
      const secondCheck = await prisma.instructorToken.findFirst({
        where: { code: newTokenCode }
      });

      if (secondCheck) {
        return NextResponse.json(
          { success: false, error: 'Failed to generate unique token code. Please try again.' },
          { status: 500 }
        );
      }
    }

    // Create instructor token
    const token = await prisma.instructorToken.create({
      data: {
        code: tokenCode,
        univId: univId,
        departmentId: departmentId,
        courseId: courseId || null,
        maxUsage: parseInt(maxUsage),
        usageCount: 0,
        expirationDate: new Date(expirationDate),
        status: 'active',
        createdBy: user.id,
        notes: notes?.trim() || null
      },
      include: {
        department: {
          select: { name: true, code: true }
        },
        course: {
          select: { name: true, code: true }
        }
      }
    });

    return NextResponse.json({
      success: true,
      token: {
        id: token.id,
        code: token.code,
        department: token.department,
        course: token.course,
        maxUsage: token.maxUsage,
        expirationDate: token.expirationDate,
        status: token.status
      }
    });

  } catch (error: any) {
    console.error('Token creation failed:', error);
    
    // Handle specific Prisma errors
    if (error.code === 'P2002') {
      return NextResponse.json(
        { success: false, error: 'Token code already exists' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create token' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { univId: string } }
) {
  try {
    const user = await verifySession();
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { univId } = params;
    const { searchParams } = new URL(request.url);
    const departmentId = searchParams.get('department');
    const courseId = searchParams.get('course');

    // Verify the university belongs to this admin
    const university = await prisma.univ.findFirst({
      where: {
        id: univId,
        adminId: user.id
      }
    });

    if (!university) {
      return NextResponse.json(
        { success: false, error: 'University not found or access denied' },
        { status: 404 }
      );
    }

    // Build where clause for filtering
    const whereClause: any = {
      univId: univId
    };

    if (departmentId) {
      whereClause.departmentId = departmentId;
    }

    if (courseId) {
      whereClause.courseId = courseId;
    }

    const tokens = await prisma.instructorToken.findMany({
      where: whereClause,
      include: {
        department: {
          select: { name: true, code: true }
        },
        course: {
          select: { name: true, code: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    return NextResponse.json({
      success: true,
      tokens
    });

  } catch (error) {
    console.error('Failed to fetch tokens:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch tokens' },
      { status: 500 }
    );
  }
}
