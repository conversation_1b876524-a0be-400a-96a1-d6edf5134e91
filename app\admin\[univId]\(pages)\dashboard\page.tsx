
import {
  BookOpen,
  GraduationCap,
  ClipboardList,
  Shield,
  Bell,
  Search,
  Settings,
  LogOut,
  Menu,
  X,
  University,
  UserCheck,
  Activity,
  TrendingUp,
  TrendingDown,
  Eye,
  MoreHorizontal,
  Building2,
} from "lucide-react"
import Menu<PERSON>utton from "./MenuButton"
import SideBarOverlay from "./SideBarOverlay"
import RefreshButton from "./RefreshButton"
import { verifySession } from "@/app/lib/session"
import { redirect } from "next/navigation"

// Server-side function to fetch university dashboard data
async function getUniversityDashboardData(univId: string) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/admin/${univId}/dashboard`, {
      cache: 'no-store', // Always fetch fresh data
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('Failed to fetch dashboard data:', response.statusText);
      return null;
    }

    const result = await response.json();
    return result.success ? result.data : null;
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    return null;
  }
}

export default async function AdminDashboard({ params }: { params: Promise<{ univId: string }> }) {
  // Verify session
  const user = await verifySession();
  if (!user) {
    redirect('/admin/login');
  }

  const { univId } = await params;

  // Fetch dashboard data for this university
  const dashboardData = await getUniversityDashboardData(univId);

  // Process the fetched data and add icons
  let kpiData, systemSummary, recentActivity, universityInfo;

  if (dashboardData) {
    // Map the fetched KPI data and add the appropriate icons
    kpiData = dashboardData.kpiData.map((kpi: any) => ({
      ...kpi,
      icon: kpi.icon === "UserCheck" ? UserCheck :
            kpi.icon === "University" ? University :
            kpi.icon === "GraduationCap" ? GraduationCap :
            kpi.icon === "ClipboardList" ? ClipboardList : Activity
    }));

    systemSummary = dashboardData.systemSummary;
    recentActivity = dashboardData.recentActivity;
    universityInfo = dashboardData.universityInfo;
  } else {
    // Fallback data if API fails
    kpiData = [
      {
        title: "Total Instructors",
        value: "0",
        change: "0%",
        trend: "up",
        icon: UserCheck,
        color: "green",
      },
      {
        title: "Total Exam given",
        value: "0",
        change: "0%",
        trend: "up",
        icon: University,
        color: "blue",
      },
      {
        title: "Total Students",
        value: "0",
        change: "0%",
        trend: "up",
        icon: GraduationCap,
        color: "purple",
      },
      {
        title: "Active Exams Today",
        value: "0",
        change: "0%",
        trend: "down",
        icon: ClipboardList,
        color: "orange",
      },
    ];

    systemSummary = [
      { label: "Blocked Accounts", value: 0, color: "red" },
      { label: "Flagged Exams", value: 0, color: "yellow" },
      { label: "Pending Reviews", value: 0, color: "blue" },
      { label: "System Alerts", value: 0, color: "red" },
    ];

    recentActivity = [
      {
        id: "fallback-1",
        action: "No recent activity available",
        time: "N/A",
        type: "university_added",
        user: "System",
        university: "Unknown",
      },
    ];

    universityInfo = {
      id: univId,
      name: "University",
      location: "Unknown",
      verified: "Pending"
    };
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "exam_created":
        return <ClipboardList className="h-4 w-4 text-blue-600" />
      case "account_blocked":
        return <Shield className="h-4 w-4 text-red-600" />
      case "university_added":
        return <University className="h-4 w-4 text-green-600" />
      case "instructor_added":
        return <UserCheck className="h-4 w-4 text-green-600" />
      case "student_enrolled":
        return <GraduationCap className="h-4 w-4 text-blue-600" />
      case "payment_completed":
        return <Activity className="h-4 w-4 text-green-600" />
      case "payment_pending":
        return <Activity className="h-4 w-4 text-yellow-600" />
      case "exam_completed":
        return <Activity className="h-4 w-4 text-purple-600" />
      case "token_generated":
        return <UserCheck className="h-4 w-4 text-orange-600" />
      default:
        return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  const sidebarItems = [
    { name: "Dashboard", icon: Activity, href: `/admin/${univId}/dashboard`, active: true },
    { name: "Departments", icon: Building2, href: `/admin/${univId}/departments` },
    { name: "Instructor Tokens", icon: UserCheck, href: `/admin/${univId}/tokens` },
    { name: "Students", icon: GraduationCap, href: `/admin/${univId}/students` },
    { name: "Live Monitoring", icon: Eye, href: `/admin/${univId}/monitoring` },
    { name: "Security Logs", icon: Shield, href: `/admin/${univId}/logs` },
    { name: "System Settings", icon: Settings, href: `/admin/${univId}/settings` },
  ]

  return (
    
        <main className="p-6">
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {universityInfo.name} Dashboard
                </h1>
                <p className="text-gray-600">
                  Monitor and manage your university examination platform
                </p>
                <div className="flex items-center gap-2 mt-2">
                  <span className="text-sm text-gray-500">{universityInfo.location}</span>
                  <span className="text-gray-400">•</span>
                  <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                    universityInfo.verified === 'Accepted'
                      ? 'bg-green-100 text-green-800'
                      : universityInfo.verified === 'Pending'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {universityInfo.verified}
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <div className="text-right">
                  <p className="text-sm text-gray-500">Last updated</p>
                  <p className="text-sm font-medium text-gray-900">
                    {dashboardData?.lastUpdated
                      ? new Date(dashboardData.lastUpdated).toLocaleTimeString()
                      : 'N/A'
                    }
                  </p>
                </div>
                <RefreshButton univId={univId} />
              </div>
            </div>
          </div>

          {/* KPI Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {kpiData.map((kpi: any, index: number) => {
              // Dynamic trend determination
              const isPositive = kpi.trend === "up";

              // Dynamic colors based on trend and context
              const getTrendColor = () => {
                if (kpi.title === "Active Exams Today") {
                  // For exams, both up and down can be normal
                  return isPositive ? "text-blue-600" : "text-orange-600";
                }
                // For instructors and students, up is good, down is concerning
                return isPositive ? "text-green-600" : "text-red-600";
              };

              const getIconBgColor = () => {
                switch (kpi.color) {
                  case "green": return "bg-green-100";
                  case "purple": return "bg-purple-100";
                  case "orange": return "bg-orange-100";
                  case "blue": return "bg-blue-100";
                  default: return "bg-gray-100";
                }
              };

              const getIconTextColor = () => {
                switch (kpi.color) {
                  case "green": return "text-green-600";
                  case "purple": return "text-purple-600";
                  case "orange": return "text-orange-600";
                  case "blue": return "text-blue-600";
                  default: return "text-gray-600";
                }
              };

              return (
                <div key={index} className="bg-white rounded-lg shadow-sm border p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{kpi.title}</p>
                      <p className="text-2xl font-bold text-gray-900 mt-1">{kpi.value}</p>
                      <div className={`flex items-center gap-1 mt-2 text-sm ${getTrendColor()}`}>
                        {isPositive ? (
                          <TrendingUp className="h-4 w-4" />
                        ) : (
                          <TrendingDown className="h-4 w-4" />
                        )}
                        <span className="font-medium">{kpi.change}</span>
                      </div>
                    </div>
                    <div className={`p-3 rounded-full ${getIconBgColor()}`}>
                      <kpi.icon className={`h-6 w-6 ${getIconTextColor()}`} />
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* System Summary */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">System Summary</h3>
                <button className="p-1 hover:bg-gray-100 rounded">
                  <MoreHorizontal className="h-4 w-4 text-gray-500" />
                </button>
              </div>
              <div className="space-y-4">
                {systemSummary.map((item: any, index: number) => {
                  // Dynamic color determination based on value and type
                  const getIndicatorColor = () => {
                    const value = item.value;

                    // For blocked accounts and system alerts - red if > 0
                    if (item.label.includes("Blocked") || item.label.includes("Alerts")) {
                      return value > 0 ? "bg-red-500" : "bg-green-500";
                    }

                    // For flagged exams - yellow if > 0, green if 0
                    if (item.label.includes("Flagged")) {
                      return value > 0 ? "bg-yellow-500" : "bg-green-500";
                    }

                    // For pending reviews - blue if > 0, green if 0
                    if (item.label.includes("Pending")) {
                      return value > 0 ? "bg-blue-500" : "bg-green-500";
                    }

                    // Default color from API
                    switch (item.color) {
                      case "red": return "bg-red-500";
                      case "yellow": return "bg-yellow-500";
                      case "blue": return "bg-blue-500";
                      case "green": return "bg-green-500";
                      default: return "bg-gray-500";
                    }
                  };

                  const getValueColor = () => {
                    const value = item.value;

                    // Red text for concerning values
                    if ((item.label.includes("Blocked") || item.label.includes("Alerts")) && value > 0) {
                      return "text-red-600 font-semibold";
                    }

                    // Yellow text for warnings
                    if (item.label.includes("Flagged") && value > 0) {
                      return "text-yellow-600 font-semibold";
                    }

                    // Blue text for pending items
                    if (item.label.includes("Pending") && value > 0) {
                      return "text-blue-600 font-semibold";
                    }

                    return "text-gray-900 font-medium";
                  };

                  return (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">{item.label}</span>
                      <div className="flex items-center gap-2">
                        <span className={`text-sm ${getValueColor()}`}>{item.value}</span>
                        <div className={`h-2 w-2 rounded-full ${getIndicatorColor()}`}></div>
                      </div>
                    </div>
                  );
                })}
              </div>
              <button className="w-full mt-4 px-4 py-2 text-sm font-medium text-blue-600 hover:bg-blue-50 rounded-md transition-colors">
                View All Alerts
              </button>
            </div>

            {/* Recent Activity */}
            <div className="lg:col-span-2 bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
                <button className="text-sm font-medium text-blue-600 hover:text-blue-700">View All</button>
              </div>
              <div className="space-y-4">
                {recentActivity.map((activity: any) => (
                  <div key={activity.id} className="flex items-start gap-3 p-3 hover:bg-gray-50 rounded-md">
                    <div className="flex-shrink-0 mt-0.5">{getActivityIcon(activity.type)}</div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900">{activity.action}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs text-gray-500">{activity.time}</span>
                        <span className="text-xs text-gray-400">•</span>
                        <span className="text-xs text-gray-500">{activity.university}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </main>

      
  )
}
