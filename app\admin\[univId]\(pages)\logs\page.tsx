"use client"

import { useState } from "react"
import {
  Search,
  Download,
  Eye,
  Calendar,
  User,
  Shield,
  Activity,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  X,
} from "lucide-react"

export default function SecurityLogs() {
  const [searchTerm, setSearchTerm] = useState("")
  const [actionFilter, setActionFilter] = useState("all")
  const [dateRange, setDateRange] = useState("7")
  const [selectedLog, setSelectedLog] = useState<any>(null)

  const logs = [
    {
      id: 1,
      timestamp: "2024-01-15T10:30:00",
      action: "Student Account Blocked",
      performedBy: "Admin User",
      affectedUser: "<EMAIL>",
      ipAddress: "*************",
      details: {
        reason: "Multiple failed login attempts",
        previousAttempts: 5,
        location: "Cambridge, MA",
        userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      },
      severity: "high",
    },
    {
      id: 2,
      timestamp: "2024-01-15T09:45:00",
      action: "Exam Terminated",
      performedBy: "Dr. <PERSON>",
      affectedUser: "Advanced Calculus Midterm",
      ipAddress: "*********",
      details: {
        reason: "Technical difficulties reported",
        participantsAffected: 45,
        timeRemaining: "30 minutes",
        examId: "CALC-2024-001",
      },
      severity: "medium",
    },
    {
      id: 3,
      timestamp: "2024-01-15T08:20:00",
      action: "Password Reset",
      performedBy: "System",
      affectedUser: "<EMAIL>",
      ipAddress: "***********",
      details: {
        method: "Email verification",
        requestedBy: "<EMAIL>",
        verificationCode: "ABC123",
        completed: true,
      },
      severity: "low",
    },
    {
      id: 4,
      timestamp: "2024-01-15T07:15:00",
      action: "University Registration",
      performedBy: "Admin User",
      affectedUser: "Stanford University",
      ipAddress: "*************",
      details: {
        contactEmail: "<EMAIL>",
        department: "Computer Science",
        tokensGenerated: 1,
        status: "Active",
      },
      severity: "low",
    },
    {
      id: 5,
      timestamp: "2024-01-14T16:30:00",
      action: "Suspicious Activity Detected",
      performedBy: "System",
      affectedUser: "<EMAIL>",
      ipAddress: "************",
      details: {
        activityType: "Multiple IP addresses",
        examId: "PHYS-2024-002",
        flaggedAt: "2024-01-14T16:25:00",
        resolved: false,
      },
      severity: "high",
    },
  ]

  const actionTypes = [
    "Student Account Blocked",
    "Exam Terminated",
    "Password Reset",
    "University Registration",
    "Suspicious Activity Detected",
  ]

  const filteredLogs = logs.filter((log) => {
    const matchesSearch =
      log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.affectedUser.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.performedBy.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesAction = actionFilter === "all" || log.action === actionFilter

    // Date filtering
    const logDate = new Date(log.timestamp)
    const now = new Date()
    const daysAgo = Number.parseInt(dateRange)
    const cutoffDate = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000)
    const matchesDate = logDate >= cutoffDate

    return matchesSearch && matchesAction && matchesDate
  })

  const getActionIcon = (action: string) => {
    switch (action) {
      case "Student Account Blocked":
        return <Shield className="h-4 w-4 text-red-600" />
      case "Exam Terminated":
        return <XCircle className="h-4 w-4 text-orange-600" />
      case "Password Reset":
        return <CheckCircle className="h-4 w-4 text-blue-600" />
      case "University Registration":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "Suspicious Activity Detected":
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      default:
        return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "high":
        return "bg-red-100 text-red-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      case "low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const exportLogs = () => {
    const csvContent = [
      ["Timestamp", "Action", "Performed By", "Affected User", "IP Address", "Severity"],
      ...filteredLogs.map((log) => [
        log.timestamp,
        log.action,
        log.performedBy,
        log.affectedUser,
        log.ipAddress,
        log.severity,
      ]),
    ]
      .map((row) => row.join(","))
      .join("\n")

    const blob = new Blob([csvContent], { type: "text/csv" })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `security_logs_${new Date().toISOString().split("T")[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="p-6">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Access & Security Logs</h1>
          <p className="text-gray-600">Monitor system activities and security events</p>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search logs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-full sm:w-80"
                />
              </div>

              <select
                value={actionFilter}
                onChange={(e) => setActionFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Actions</option>
                {actionTypes.map((action) => (
                  <option key={action} value={action}>
                    {action}
                  </option>
                ))}
              </select>

              <select
                value={dateRange}
                onChange={(e) => setDateRange(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="1">Last 24 hours</option>
                <option value="7">Last 7 days</option>
                <option value="30">Last 30 days</option>
                <option value="90">Last 90 days</option>
              </select>
            </div>

            <button
              onClick={exportLogs}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Download className="h-4 w-4" />
              Export CSV
            </button>
          </div>
        </div>

        {/* Logs Table */}
        <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b">
                <tr>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Timestamp</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Action</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Performed By</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Affected User</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">IP Address</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Severity</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {filteredLogs.map((log) => (
                  <tr key={log.id} className="hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div className="text-sm">
                        <p className="text-gray-900 flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(log.timestamp).toLocaleDateString()}
                        </p>
                        <p className="text-gray-500 flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {new Date(log.timestamp).toLocaleTimeString()}
                        </p>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        {getActionIcon(log.action)}
                        <span className="text-sm font-medium text-gray-900">{log.action}</span>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3 text-gray-400" />
                        <span className="text-sm text-gray-900">{log.performedBy}</span>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <span className="text-sm text-gray-900">{log.affectedUser}</span>
                    </td>
                    <td className="py-4 px-4">
                      <code className="text-sm bg-gray-100 px-2 py-1 rounded">{log.ipAddress}</code>
                    </td>
                    <td className="py-4 px-4">
                      <span
                        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(log.severity)}`}
                      >
                        {log.severity.toUpperCase()}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <button
                        onClick={() => setSelectedLog(log)}
                        className="p-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-6">
          <p className="text-sm text-gray-700">
            Showing <span className="font-medium">1</span> to <span className="font-medium">{filteredLogs.length}</span>{" "}
            of <span className="font-medium">{filteredLogs.length}</span> results
          </p>
          <div className="flex items-center gap-2">
            <button
              className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
              disabled
            >
              Previous
            </button>
            <button className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">1</button>
            <button
              className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
              disabled
            >
              Next
            </button>
          </div>
        </div>
      </div>

      {/* Log Details Modal */}
      {selectedLog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b">
              <h2 className="text-xl font-semibold text-gray-900">Log Details</h2>
              <button onClick={() => setSelectedLog(null)} className="p-2 hover:bg-gray-100 rounded-md">
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Timestamp</label>
                  <p className="text-sm text-gray-900">{new Date(selectedLog.timestamp).toLocaleString()}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Action</label>
                  <p className="text-sm text-gray-900">{selectedLog.action}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Performed By</label>
                  <p className="text-sm text-gray-900">{selectedLog.performedBy}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Affected User</label>
                  <p className="text-sm text-gray-900">{selectedLog.affectedUser}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">IP Address</label>
                  <code className="text-sm bg-gray-100 px-2 py-1 rounded">{selectedLog.ipAddress}</code>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Severity</label>
                  <span
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(selectedLog.severity)}`}
                  >
                    {selectedLog.severity.toUpperCase()}
                  </span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Additional Details</label>
                <div className="bg-gray-50 rounded-lg p-4">
                  <pre className="text-sm text-gray-900 whitespace-pre-wrap">
                    {JSON.stringify(selectedLog.details, null, 2)}
                  </pre>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
