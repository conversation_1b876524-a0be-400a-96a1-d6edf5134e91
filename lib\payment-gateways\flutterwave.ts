import axios from 'axios';

interface FlutterwavePaymentRequest {
  tx_ref: string;
  amount: number;
  currency: string;
  redirect_url: string;
  customer: {
    email: string;
    phonenumber?: string;
    name?: string;
  };
  customizations: {
    title: string;
    description: string;
    logo?: string;
  };
  payment_options: string;
  meta?: {
    consumer_id: string;
    consumer_mac: string;
  };
}

interface FlutterwavePaymentResponse {
  status: string;
  message: string;
  data?: {
    link: string;
    id: number;
  };
}

interface FlutterwaveVerificationResponse {
  status: string;
  message: string;
  data?: {
    id: number;
    tx_ref: string;
    flw_ref: string;
    device_fingerprint: string;
    amount: number;
    currency: string;
    charged_amount: number;
    app_fee: number;
    merchant_fee: number;
    processor_response: string;
    auth_model: string;
    ip: string;
    narration: string;
    status: string;
    payment_type: string;
    created_at: string;
    account_id: number;
    customer: {
      id: number;
      name: string;
      phone_number: string;
      email: string;
      created_at: string;
    };
  };
}

export class FlutterwaveService {
  private baseUrl: string;
  private secretKey: string;
  private publicKey: string;

  constructor() {
    this.baseUrl = process.env.NODE_ENV === 'production' 
      ? 'https://api.flutterwave.com/v3' 
      : 'https://api.flutterwave.com/v3';
    this.secretKey = process.env.FLUTTERWAVE_SECRET_KEY!;
    this.publicKey = process.env.FLUTTERWAVE_PUBLIC_KEY!;
  }

  async initiatePayment(
    amount: number,
    email: string,
    phoneNumber: string,
    txRef: string,
    currency: string = 'XAF',
    customerName?: string
  ): Promise<{ success: boolean; paymentUrl?: string; error?: string }> {
    try {
      // Validate required environment variables
      if (!this.secretKey || !this.publicKey) {
        throw new Error('Flutterwave API keys not configured. Please set FLUTTERWAVE_PUBLIC_KEY and FLUTTERWAVE_SECRET_KEY in your environment variables.');
      }

      // Ensure we have a valid base URL
      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

      console.log('Initiating Flutterwave payment:', {
        txRef,
        amount,
        currency,
        email,
        baseUrl,
        redirectUrl: `${baseUrl}/payment/callback`
      });

      const paymentRequest: FlutterwavePaymentRequest = {
        tx_ref: txRef,
        amount,
        currency,
        redirect_url: `${baseUrl}/payment/callback`,
        customer: {
          email,
          phonenumber: phoneNumber || '',
          name: customerName || email.split('@')[0],
        },
        customizations: {
          title: 'Exam online Subscription',
          description: 'Subscription payment for Exam Online platform',
          logo: `${baseUrl}/logo.png`,
        },
        payment_options: 'card,mobilemoney,ussd,banktransfer,account,mpesa,qr',
        meta: {
          consumer_id: txRef,
          consumer_mac: 'subscription_payment',
        },
      };

      console.log('Sending payment request to Flutterwave:', {
        url: `${this.baseUrl}/payments`,
        txRef: paymentRequest.tx_ref,
        amount: paymentRequest.amount,
        redirectUrl: paymentRequest.redirect_url
      });

      // Add retry logic for network issues
      let lastError: any;
      const maxRetries = 3;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          console.log(`Flutterwave API attempt ${attempt}/${maxRetries}`);

          const response = await axios.post<FlutterwavePaymentResponse>(
            `${this.baseUrl}/payments`,
            paymentRequest,
            {
              headers: {
                'Authorization': `Bearer ${this.secretKey}`,
                'Content-Type': 'application/json',
              },
              timeout: 30000, // 30 second timeout
              httpsAgent: new (require('https').Agent)({
                rejectUnauthorized: false, // For development only
                keepAlive: true,
                timeout: 30000,
              }),
            }
          );

          console.log('Flutterwave response:', {
            status: response.data.status,
            message: response.data.message,
            hasLink: !!response.data.data?.link
          });

          if (response.data.status === 'success' && response.data.data?.link) {
            console.log('Payment URL generated:', response.data.data.link);
            return {
              success: true,
              paymentUrl: response.data.data.link,
            };
          }

          console.error('Payment initiation failed:', response.data);
          return {
            success: false,
            error: response.data.message || 'Payment initiation failed',
          };

        } catch (error: any) {
          lastError = error;
          console.error(`Flutterwave API attempt ${attempt} failed:`, {
            message: error.message,
            code: error.code,
            status: error.response?.status,
            data: error.response?.data
          });

          // If it's the last attempt, don't retry
          if (attempt === maxRetries) {
            break;
          }

          // Wait before retrying (exponential backoff)
          const delay = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
          console.log(`Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }

      // All retries failed
      console.error('All Flutterwave API attempts failed. Last error:', lastError);

      // Provide specific error messages based on error type
      let errorMessage = 'Payment initiation failed';

      if (lastError.code === 'ECONNRESET' || lastError.code === 'ECONNREFUSED') {
        errorMessage = 'Network connection error. Please check your internet connection and try again.';
      } else if (lastError.code === 'ETIMEDOUT') {
        errorMessage = 'Request timeout. Please try again.';
      } else if (lastError.response?.status === 401) {
        errorMessage = 'Invalid API credentials. Please check your Flutterwave API keys.';
      } else if (lastError.response?.status === 400) {
        errorMessage = lastError.response?.data?.message || 'Invalid payment request.';
      } else if (lastError.response?.status >= 500) {
        errorMessage = 'Flutterwave service is temporarily unavailable. Please try again later.';
      }

      return {
        success: false,
        error: errorMessage,
      };
    } catch (error: any) {
      console.error('Unexpected error in payment initiation:', error);
      return {
        success: false,
        error: 'An unexpected error occurred. Please try again.',
      };
    }
  }

  async verifyPayment(transactionId: string): Promise<FlutterwaveVerificationResponse> {
    try {
      const response = await axios.get<FlutterwaveVerificationResponse>(
        `${this.baseUrl}/transactions/${transactionId}/verify`,
        {
          headers: {
            'Authorization': `Bearer ${this.secretKey}`,
          },
        }
      );

      return response.data;
    } catch (error: any) {
      console.error('Flutterwave verification failed:', error.response?.data || error.message);
      throw new Error('Payment verification failed');
    }
  }

  async verifyPaymentByTxRef(txRef: string): Promise<FlutterwaveVerificationResponse> {
    try {
      const response = await axios.get<FlutterwaveVerificationResponse>(
        `${this.baseUrl}/transactions/verify_by_reference?tx_ref=${txRef}`,
        {
          headers: {
            'Authorization': `Bearer ${this.secretKey}`,
          },
        }
      );

      return response.data;
    } catch (error: any) {
      console.error('Flutterwave verification by ref failed:', error.response?.data || error.message);
      throw new Error('Payment verification failed');
    }
  }

  generateTxRef(prefix: string = 'fw'): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  validatePhoneNumber(phoneNumber: string): boolean {
    // Basic validation for African phone numbers
    const phonePattern = /^(\+?234|234|0)?[789]\d{9}$|^(\+?237|237)?[6-9]\d{8}$|^(\+?254|254|0)?[17]\d{8}$/;
    return phonePattern.test(phoneNumber.replace(/\s+/g, ''));
  }

  // Webhook signature verification
  verifyWebhookSignature(payload: string, signature: string): boolean {
    const crypto = require('crypto');
    const hash = crypto
      .createHmac('sha256', this.secretKey)
      .update(payload, 'utf8')
      .digest('hex');
    
    return hash === signature;
  }
}
