import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/app/lib/prisma';
import { verifySession } from '@/app/lib/session';

export async function GET(
  request: NextRequest,
  { params }: { params: { univId: string, departmentId: string } }
) {
  try {
    const user = await verifySession();
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { univId, departmentId } = params;

    // Verify the university belongs to this admin
    const university = await prisma.univ.findFirst({
      where: {
        id: univId,
        adminId: user.id
      }
    });

    if (!university) {
      return NextResponse.json(
        { success: false, error: 'University not found or access denied' },
        { status: 404 }
      );
    }

    // Verify department belongs to this university
    const department = await prisma.department.findFirst({
      where: {
        id: departmentId,
        univId: univId,
        isActive: true
      }
    });

    if (!department) {
      return NextResponse.json(
        { success: false, error: 'Department not found' },
        { status: 404 }
      );
    }

    const courses = await prisma.course.findMany({
      where: {
        departmentId: departmentId,
        isActive: true
      },
      orderBy: { name: 'asc' }
    });

    return NextResponse.json({
      success: true,
      courses
    });

  } catch (error) {
    console.error('Failed to fetch courses:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch courses' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { univId: string, departmentId: string } }
) {
  try {
    const user = await verifySession();
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { univId, departmentId } = params;

    // Verify the university belongs to this admin
    const university = await prisma.univ.findFirst({
      where: {
        id: univId,
        adminId: user.id
      }
    });

    if (!university) {
      return NextResponse.json(
        { success: false, error: 'University not found or access denied' },
        { status: 404 }
      );
    }

    // Verify department belongs to this university
    const department = await prisma.department.findFirst({
      where: {
        id: departmentId,
        univId: univId,
        isActive: true
      }
    });

    if (!department) {
      return NextResponse.json(
        { success: false, error: 'Department not found' },
        { status: 404 }
      );
    }

    const body = await request.json();
    const {
      name,
      code,
      description,
      credits,
      semester,
      year
    } = body;

    // Validate required fields
    if (!name || !code) {
      return NextResponse.json(
        { success: false, error: 'Name and code are required' },
        { status: 400 }
      );
    }

    // Check if course code already exists in this department
    const existingCourse = await prisma.course.findFirst({
      where: {
        departmentId: departmentId,
        code: code.toUpperCase()
      }
    });

    if (existingCourse) {
      return NextResponse.json(
        { success: false, error: 'Course code already exists in this department' },
        { status: 400 }
      );
    }

    // Create course
    const course = await prisma.course.create({
      data: {
        name: name.trim(),
        code: code.toUpperCase().trim(),
        description: description?.trim() || null,
        credits: credits ? parseInt(credits) : 3,
        semester: semester?.trim() || null,
        year: year ? parseInt(year) : null,
        departmentId: departmentId,
        isActive: true
      }
    });

    return NextResponse.json({
      success: true,
      course: {
        id: course.id,
        name: course.name,
        code: course.code,
        description: course.description,
        credits: course.credits
      }
    });

  } catch (error: any) {
    console.error('Course creation failed:', error);
    
    // Handle specific Prisma errors
    if (error.code === 'P2002') {
      return NextResponse.json(
        { success: false, error: 'Course code already exists' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create course' },
      { status: 500 }
    );
  }
}
