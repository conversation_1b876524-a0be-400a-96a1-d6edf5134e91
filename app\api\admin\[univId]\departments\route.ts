import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/app/lib/prisma';
import { verifySession } from '@/app/lib/session';

export async function POST(
  request: NextRequest,
  { params }: { params: { univId: string } }
) {
  try {
    // Verify admin session
    const user = await verifySession();
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { univId } = params;

    // Verify the university belongs to this admin
    const university = await prisma.univ.findFirst({
      where: {
        id: univId,
        adminId: user.id
      }
    });

    if (!university) {
      return NextResponse.json(
        { success: false, error: 'University not found or access denied' },
        { status: 404 }
      );
    }

    const body = await request.json();
    const {
      name,
      code,
      description,
      headOfDepartment,
      email,
      phone,
      location,
      establishedYear
    } = body;

    // Validate required fields
    if (!name || !code) {
      return NextResponse.json(
        { success: false, error: 'Name and code are required' },
        { status: 400 }
      );
    }

    // Check if department code already exists in this university
    const existingDepartment = await prisma.department.findFirst({
      where: {
        univId: univId,
        code: code.toUpperCase()
      }
    });

    if (existingDepartment) {
      return NextResponse.json(
        { success: false, error: 'Department code already exists in this university' },
        { status: 400 }
      );
    }

    // Create department
    const department = await prisma.department.create({
      data: {
        name: name.trim(),
        code: code.toUpperCase().trim(),
        description: description?.trim() || null,
        headOfDepartment: headOfDepartment?.trim() || null,
        email: email?.trim() || null,
        phone: phone?.trim() || null,
        location: location?.trim() || null,
        establishedYear: establishedYear || null,
        univId: univId,
        isActive: true
      }
    });

    return NextResponse.json({
      success: true,
      department: {
        id: department.id,
        name: department.name,
        code: department.code,
        description: department.description
      }
    });

  } catch (error: any) {
    console.error('Department creation failed:', error);
    
    // Handle specific Prisma errors
    if (error.code === 'P2002') {
      return NextResponse.json(
        { success: false, error: 'Department code already exists' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create department' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { univId: string } }
) {
  try {
    const user = await verifySession();
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { univId } = params;

    // Verify the university belongs to this admin
    const university = await prisma.univ.findFirst({
      where: {
        id: univId,
        adminId: user.id
      }
    });

    if (!university) {
      return NextResponse.json(
        { success: false, error: 'University not found or access denied' },
        { status: 404 }
      );
    }

    const departments = await prisma.department.findMany({
      where: {
        univId: univId,
        isActive: true
      },
      include: {
        courses: {
          where: { isActive: true },
          select: { id: true, name: true, code: true }
        },
        instructorTokens: {
          select: { id: true, status: true }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    const enrichedDepartments = departments.map(dept => ({
      ...dept,
      courseCount: dept.courses.length,
      tokenCount: dept.instructorTokens.length,
      activeTokenCount: dept.instructorTokens.filter(token => token.status === 'active').length
    }));

    return NextResponse.json({
      success: true,
      departments: enrichedDepartments
    });

  } catch (error) {
    console.error('Failed to fetch departments:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch departments' },
      { status: 500 }
    );
  }
}
