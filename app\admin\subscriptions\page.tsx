import Link from "next/link"
import { BookOpen, Search, CreditCard, Calendar, DollarSign, CheckCircle, XCircle, Clock, Plus, Filter, ChevronRight } from "lucide-react"
import prisma from "@/app/lib/prisma"
import { verifySession } from "@/app/lib/session";
import { redirect} from "next/navigation";
import { createToken } from "@/app/lib/token";
import { toast } from "sonner";
import ButtonClick from "./ButtonClick";

type SubscriptionType = {
  id: string;
  status: string;
  activeDate: string;
  createdAt: Date;
  admin: {
    id: string;
    name: string;
    email: string;
  };
  plan: {
    id: string;
    name: string;
    price: number;
    currency: string;
    billing: string;
  } | null;
  payment: {
    id: string;
    amount: number;
    status: string;
    method: string;
    createdAt: Date;
  } | null;
}

const enrichSubscriptions = (subscriptions: SubscriptionType[]) => {
  const statusColors = {
    active: "bg-green-100 text-green-800",
    unactive: "bg-red-100 text-red-800",
    pending: "bg-yellow-100 text-yellow-800",
    expired: "bg-gray-100 text-gray-800"
  };

  const paymentStatusColors = {
    completed: "bg-green-100 text-green-800",
    pending: "bg-yellow-100 text-yellow-800",
    failed: "bg-red-100 text-red-800",
    cancelled: "bg-gray-100 text-gray-800"
  };

  return subscriptions.map(subscription => ({
    ...subscription,
    statusColor: statusColors[subscription.status as keyof typeof statusColors] || statusColors.unactive,
    paymentStatusColor: subscription.payment 
      ? paymentStatusColors[subscription.payment.status as keyof typeof paymentStatusColors] || paymentStatusColors.pending
      : paymentStatusColors.pending,
    formattedPrice: subscription.plan 
      ? `${subscription.plan.currency} ${subscription.plan.price.toLocaleString()}`
      : "N/A",
    billingCycle: subscription.plan?.billing || "N/A"
  }));
}

export default async function SubscriptionsPage() {
  const user = await verifySession()
  if(!user) redirect('/admin/login')
  
  // Fetch all subscriptions with related data
  const rawSubscriptions = await prisma.subscription.findMany({
    select: {
      id: true,
      status: true,
      activeDate: true,
      createdAt: true,
      admin: {
        select: {
          id: true,
          name: true,
          email: true
        }
      },
      plan: {
        select: {
          id: true,
          name: true,
          price: true,
          currency: true,
          billing: true
        }
      },
      payment: {
        select: {
          id: true,
          amount: true,
          status: true,
          method: true,
          createdAt: true
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  })
  
  const subscriptions = enrichSubscriptions(rawSubscriptions)
  
  // Calculate statistics
  const stats = {
    total: subscriptions.length,
    active: subscriptions.filter(s => s.status === 'active').length,
    unactive: subscriptions.filter(s => s.status === 'unactive').length,
    expired: subscriptions.filter(s => s.status === 'expired').length
  }
  
  return (
    <div className="min-h-screen w-full bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-2">
              <div className="flex h-8 w-8 items-center justify-center rounded bg-green-600 text-white">
                <BookOpen className="h-4 w-4" />
              </div>
              <span className="text-xl font-bold text-green-600">ExamPro</span>
            </div>
            <div className="flex items-center gap-4">
              <Link href="/admin/all-university" className="text-sm text-gray-600 hover:text-gray-900">
                Universities
              </Link>
              <Link href="/help" className="text-sm text-gray-600 hover:text-gray-900">
                Need help?
              </Link>
              <Link href="/contact" className="text-sm font-medium text-green-600 hover:text-green-700">
                Contact Support
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Subscription Management</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Monitor and manage all subscription plans and payments across the platform
          </p>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
                <CreditCard className="h-5 w-5 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Subscriptions</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-green-100">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active</p>
                <p className="text-2xl font-bold text-gray-900">{stats.active}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-yellow-100">
                <Clock className="h-5 w-5 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Un-active</p>
                <p className="text-2xl font-bold text-gray-900">{stats.unactive}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-red-100">
                <XCircle className="h-5 w-5 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Expired</p>
                <p className="text-2xl font-bold text-gray-900">{stats.expired}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filter Bar */}
        <div className="w-full mb-8 flex gap-4 justify-between items-center">
          <div className="flex gap-4">
            <div className="relative w-[448px] max-w-[32rem]">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search by admin name, email, or plan..."
                className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              />
            </div>
            <button className="text-sm hover:bg-gray-100 py-3 !w-fit font-medium flex items-center border border-gray-300 rounded-lg text-gray-800 px-3">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </button>
          </div>
          <Link href="/admin/plans" className="text-sm hover:bg-gray-100 py-3 !w-fit font-medium flex items-center border border-gray-300 rounded-lg text-gray-800 px-3">
            <Plus className="h-4 w-4 mr-2" />
            Manage Plans
          </Link>
        </div>

        {/* Subscriptions Grid */}
        <div className="grid w-full min-w-full items-center gap-4 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 mb-12">
          {subscriptions.map((subscription) => (
            <SubscriptionCard key={subscription.id} subscription={subscription} user={user} />
          ))}
        </div>

        {/* Empty State */}
        {subscriptions.length === 0 && (
          <div className="text-center">
            <div className="bg-white rounded-lg border border-gray-200 p-6 max-w-md mx-auto">
              <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No subscriptions found</h3>
              <p className="text-sm text-gray-600 mb-4">
                There are no subscription records in the system yet.
              </p>
              <Link 
                href="/payment"
                className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors text-sm font-medium inline-block"
              >
                Manage Plans
              </Link>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}

function SubscriptionCard({ subscription,user }: { subscription: any,user:{
  id:string,
  email:string,
} }) {
  return (
    <div className="bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer group">
      <div className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
              <CreditCard className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                {subscription.admin.name}
              </h3>
              <p className="text-sm text-gray-500">{subscription.admin.email}</p>
            </div>
          </div>
          <div className={`px-2 py-1 rounded-full text-xs font-medium ${subscription.statusColor}`}>
            {subscription.status}
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Plan:</span>
            <span className="text-sm font-medium text-gray-900">
              {subscription.plan?.name || "No Plan"}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Price:</span>
            <span className="text-sm font-medium text-gray-900">
              {subscription.formattedPrice}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Billing:</span>
            <span className="text-sm font-medium text-gray-900 capitalize">
              {subscription.billingCycle}
            </span>
          </div>
          
          {subscription.payment && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Payment:</span>
              <div className={`px-2 py-1 rounded-full text-xs font-medium ${subscription.paymentStatusColor}`}>
                {subscription.payment.status}
              </div>
            </div>
          )}
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Active Date:</span>
            <span className="text-sm font-medium text-gray-900">
              {subscription.activeDate}
            </span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-6 grid grid-cols-2 gap-3">
          <div className="flex items-center gap-4">
            <Link
            href={`/admin/subscriptions/${subscription.id}`}
            className="flex items-center justify-center gap-2 bg-blue-50 text-blue-700 py-2 px-3 rounded-md hover:bg-blue-100 transition-colors text-sm font-medium group"
          >
            <Calendar className="h-4 w-4" />
            View Details
            <ChevronRight className="h-3 w-3 group-hover:translate-x-0.5 transition-transform" />
          </Link>
          <Link
            href={`/admin/payments?subscription=${subscription.id}`}
            className="flex items-center justify-center gap-2 bg-green-50 text-green-700 py-2 px-3 rounded-md hover:bg-green-100 transition-colors text-sm font-medium group"
          >
            <DollarSign className="h-4 w-4" />
            Payments
            <ChevronRight className="h-3 w-3 group-hover:translate-x-0.5 transition-transform" />
          </Link>
          </div>
          <div className="flex items-center gap-4">
           <ButtonClick subscription={subscription} user={user}/>
          </div>
        </div>
      </div>
    </div>
  )
}
