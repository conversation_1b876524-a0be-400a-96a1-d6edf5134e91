'use client'

import { Activity, BookOpen, Eye, GraduationCap, Settings, Shield, University, UserCheck, X } from "lucide-react"
import { useState } from "react"
import useSideBarStore from "./sidebar.store"
import { useShallow } from "zustand/shallow"


const SideBar = () => {
    const { sidebarOpen, setSidebarOpen } = useSideBarStore(useShallow(state => ({
        sidebarOpen: state.open,
        setSidebarOpen: state.setOpen
    })))
    const sidebarItems = [
        { name: "Dashboard", icon: Activity, href: "/dashboard", active: true },
        // { name: "Universities", icon: University, href: "/universities" },
        { name: "Instructor Tokens", icon: UserCheck, href: "tokens" },
        { name: "Students", icon: GraduationCap, href: "students" },
        { name: "Live Monitoring", icon: Eye, href: "monitoring" },
        { name: "Security Logs", icon: Shield, href: "logs" },
        { name: "System Settings", icon: Settings, href: "settings" },
      ]
    return(
        <div
        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform ${sidebarOpen ? "translate-x-0" : "-translate-x-full"} transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`}
      >
        <div className="flex items-center justify-between h-16 px-6 border-b">
          <div className="flex items-center gap-2">
            <div className="flex h-8 w-8 items-center justify-center rounded bg-green-600 text-white">
              <BookOpen className="h-4 w-4" />
            </div>
            <span className="text-xl font-bold text-green-600">ExamPro Admin</span>
          </div>
          <button onClick={() => setSidebarOpen(false)} className="lg:hidden p-1 rounded-md hover:bg-gray-100">
            <X className="h-5 w-5" />
          </button>
        </div>

        <nav className="mt-6 px-3">
          <div className="space-y-1">
            {sidebarItems.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className={`flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  item.active ? "bg-green-50 text-green-600" : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                }`}
              >
                <item.icon className="h-5 w-5" />
                {item.name}
              </a>
            ))}
          </div>
        </nav>
      </div>
    )
}

export default SideBar