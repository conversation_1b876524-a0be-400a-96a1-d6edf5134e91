# SmartOnline Technical Documentation

## System Architecture

### Overview
SmartOnline implements a modern web application architecture using Next.js 15 with the App Router pattern. The system follows a modular, service-oriented architecture with clear separation of concerns.

## Core Components

### 1. Frontend Architecture
- **App Router**: Server and client components
- **Components**: Reusable UI components
- **State Management**: Zustand for client-state
- **Theme System**: next-themes for dark/light mode

### 2. Backend Architecture
- **API Routes**: Next.js API routes
- **Database Layer**: Prisma ORM
- **Authentication**: JWT with jose
- **File Storage**: Sanity for assets

### 3. Service Layer
```typescript
// Activity Service
interface ActivityService {
  trackUserActivity(userId: string, action: string): Promise<void>;
  getUserActivities(userId: string): Promise<Activity[]>;
}

// Email Service
interface EmailService {
  sendVerificationEmail(email: string, token: string): Promise<void>;
  sendSubscriptionReminder(email: string, daysLeft: number): Promise<void>;
}

// Payment Service
interface PaymentService {
  processPayment(paymentDetails: PaymentDetails): Promise<PaymentResult>;
  verifyPayment(transactionId: string): Promise<VerificationResult>;
}

// Subscription Service
interface SubscriptionService {
  createSubscription(userId: string, planId: string): Promise<Subscription>;
  checkSubscriptionStatus(subscriptionId: string): Promise<SubscriptionStatus>;
}
```

## Database Design

### Entity Relationships
```mermaid
erDiagram
    Univ ||--o{ Department : has
    Univ ||--o{ Student : enrolls
    Department ||--o{ Course : offers
    Course ||--o{ InstructorToken : generates
    Admin ||--|| Univ : manages
    Student }|--|| Department : belongs
```

### Key Models
```typescript
interface University {
  id: string;
  name: string;
  domain: string;
  admin: Admin;
  departments: Department[];
  students: Student[];
  instructors: Instructor[];
}

interface Department {
  id: string;
  name: string;
  code: string;
  courses: Course[];
  university: University;
}

interface Course {
  id: string;
  name: string;
  code: string;
  department: Department;
  instructorTokens: InstructorToken[];
}
```

## Authentication Flow

### Admin Authentication
```mermaid
sequenceDiagram
    Admin->>+API: Login Request
    API->>+Database: Verify Credentials
    Database-->>-API: Admin Data
    API->>API: Generate JWT
    API-->>-Admin: Auth Token
```

### Student Authentication
```mermaid
sequenceDiagram
    Student->>+API: Login with University ID
    API->>+Database: Verify University
    Database-->>-API: University Data
    API->>API: Verify Credentials
    API-->>-Student: Auth Token
```

## Payment Processing

### Payment Flow
```mermaid
graph TD
    A[User] -->|Selects Plan| B[Payment Form]
    B -->|Submits Payment| C[Payment Service]
    C -->|Processes| D[Payment Gateway]
    D -->|Confirms| E[Webhook Handler]
    E -->|Updates| F[Database]
    F -->|Activates| G[Subscription]
```

### Subscription Management
```typescript
class SubscriptionManager {
  async createSubscription(userId: string, planId: string) {
    // Create subscription record
  }

  async checkExpiration() {
    // Check for expiring subscriptions
  }

  async sendReminders() {
    // Send renewal reminders
  }
}
```

## Security Implementation

### JWT Authentication
```typescript
class AuthService {
  async generateToken(payload: TokenPayload): Promise<string> {
    return jwt.sign(payload, process.env.JWT_SECRET!, {
      expiresIn: '24h'
    });
  }

  async verifyToken(token: string): Promise<TokenPayload> {
    return jwt.verify(token, process.env.JWT_SECRET!);
  }
}
```

### Role-Based Access Control
```typescript
enum Role {
  ADMIN = 'ADMIN',
  INSTRUCTOR = 'INSTRUCTOR',
  STUDENT = 'STUDENT'
}

interface Permission {
  role: Role;
  actions: string[];
}

class AccessControl {
  checkPermission(user: User, action: string): boolean {
    // Verify user permissions
  }
}
```

## API Design

### RESTful Endpoints
```typescript
// University Management
POST   /api/admin/create-university
GET    /api/admin/[univId]/dashboard
GET    /api/admin/[univId]/departments

// Student Management
POST   /api/auth/student/create
POST   /api/auth/student/login

// Payment Processing
POST   /api/payment
POST   /api/payment/verify

// Subscription Management
GET    /api/subscriptions
PUT    /api/subscriptions/[id]
```

## Performance Optimizations

### Caching Strategy
```typescript
class CacheManager {
  async getCachedData(key: string): Promise<any> {
    // Implement caching logic
  }

  async setCachedData(key: string, data: any): Promise<void> {
    // Cache data with expiration
  }
}
```

### Database Optimization
- Indexed queries
- Connection pooling
- Query optimization

## Error Handling

### Global Error Handler
```typescript
class ErrorHandler {
  handle(error: Error): ErrorResponse {
    // Log error
    // Format response
    // Return appropriate status
  }
}
```

### Error Types
```typescript
interface AppError extends Error {
  statusCode: number;
  code: string;
}

class ValidationError extends AppError {
  constructor(message: string) {
    super(message);
    this.statusCode = 400;
    this.code = 'VALIDATION_ERROR';
  }
}
```

## Testing Strategy

### Unit Tests
```typescript
describe('AuthService', () => {
  it('should generate valid JWT token', async () => {
    // Test token generation
  });

  it('should verify valid token', async () => {
    // Test token verification
  });
});
```

### Integration Tests
```typescript
describe('Payment Flow', () => {
  it('should process payment and create subscription', async () => {
    // Test complete payment flow
  });
});
```

## Deployment Architecture

### Production Setup
```mermaid
graph TD
    A[Load Balancer] -->|Routes Traffic| B[Next.js App]
    B -->|Queries| C[PostgreSQL]
    B -->|Assets| D[Sanity CMS]
    B -->|Emails| E[SMTP Server]
```

### Scaling Strategy
- Horizontal scaling
- Load balancing
- Database replication
- Caching layers

## Monitoring and Logging

### Metrics Collection
```typescript
class MetricsCollector {
  trackAPIMetrics(endpoint: string, duration: number): void {
    // Record API metrics
  }

  trackDatabaseMetrics(query: string, duration: number): void {
    // Record database metrics
  }
}
```

### Logging System
```typescript
class Logger {
  info(message: string, context?: any): void {
    // Log info level
  }

  error(error: Error, context?: any): void {
    // Log error level
  }
}
```

## Development Guidelines

### Code Style
- Follow TypeScript best practices
- Use ESLint configuration
- Follow component naming conventions
- Implement proper error handling

### Git Workflow
1. Feature branches
2. Pull request reviews
3. CI/CD pipeline checks
4. Version tagging

### Documentation
- Code comments
- API documentation
- Component documentation
- Change log maintenance
