"use client";

import { useState } from 'react';
import { RefreshCw } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface RefreshButtonProps {
  univId: string;
}

export default function RefreshButton({ univId }: RefreshButtonProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const router = useRouter();

  const handleRefresh = async () => {
    setIsRefreshing(true);
    
    try {
      // Refresh the page data
      router.refresh();
      
      // Optional: Call the API directly for immediate feedback
      const response = await fetch(`/api/admin/${univId}/dashboard`);
      if (response.ok) {
        // Data refreshed successfully
        setTimeout(() => {
          setIsRefreshing(false);
        }, 1000); // Show loading for at least 1 second for user feedback
      } else {
        setIsRefreshing(false);
      }
    } catch (error) {
      console.error('Refresh failed:', error);
      setIsRefreshing(false);
    }
  };

  return (
    <button
      onClick={handleRefresh}
      disabled={isRefreshing}
      className={`inline-flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-all ${
        isRefreshing
          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
          : 'bg-blue-50 text-blue-600 hover:bg-blue-100 active:scale-95'
      }`}
      title="Refresh dashboard data"
    >
      <RefreshCw 
        className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} 
      />
      {isRefreshing ? 'Refreshing...' : 'Refresh'}
    </button>
  );
}
