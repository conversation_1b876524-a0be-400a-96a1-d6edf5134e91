import React from "react"
import { ErrorType, InputProps } from "../component/ui/Input"

export type InputType = InputProps & {
    name:string
    inputType: "email"|"password"|"text" |"textarea"
    label?:{
        text:string,
        isMandatoryDesign:boolean
    } & React.InputHTMLAttributes<HTMLLabelElement>
    description?:string
    confirmPassword?:boolean
    onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void
} & React.InputHTMLAttributes<HTMLInputElement>


export type formData = {
    onSubmit?: (e:any) => void
    onClose?:() => void
    cancelButton?:boolean
    children?:React.ReactNode
    submitButton?:boolean
    FormTitle?:string
    FormDescrition?:string
    header?:React.ReactNode
    formInputs:InputType[]
} & React.FormHTMLAttributes<HTMLFormElement>