import prisma from "@/app/lib/prisma"
import { NextResponse } from "next/server"
import bcrypt from "bcrypt"
import { createSession } from "@/app/lib/session"

export async function POST(request:Request) {
    const {email,password,univID} = await request.json()

    try {
        const existingUserAdmin = await prisma.admin.findUnique(
            {
                
            where:{
                email:email,
                univ: {
                    id: univID,
                },
            }
            
            }
        )
        if(existingUserAdmin){
            const res = password == existingUserAdmin.password

            if(res) {
                await createSession({
                    id:existingUserAdmin.id,
                    email:existingUserAdmin.email,
                    role:'admin',
                    university:{
                        id:univID
                    }
                })
                return NextResponse.json({message:'Login Succesfully',role:'admin'},{status:200})}
        }
        const existingUser = await prisma.student.findUnique({where:{email:email}})
    if(!existingUser) return NextResponse.json({message:"User not found"},{status:404})
    if(existingUser.univId !== univID) return NextResponse.json({message:"User not found"},{status:404})
    const res  = await bcrypt.compare(password,existingUser.password)
    if(!res) return NextResponse.json({message:"Invalid Credential"},{status:400})
        await createSession({
                    id:existingUser.id,
                    email:existingUser.email,
                    role:'role',
                    university:{
                        id:univID
                    }
                })
    return NextResponse.json({message:"Login successful",role:'student'},{status:200})
    } catch (error) {
        console.log(error)
        return NextResponse.json({message:"Something went wrong"},{status:500})
    }
}