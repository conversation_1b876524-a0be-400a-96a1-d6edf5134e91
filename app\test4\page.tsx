
import React from 'react'
import Form, { FormHeader } from '../component/ui/Form'
import { formData } from './type'
import { LockIcon, Mail, User } from 'lucide-react'
import Input from '../component/ui/Input'

type Props = {}

const page = (props: Props) => {
  const formInput:formData['formInputs'] = [
    {
      inputType:"text",
      error:null,
      label:{
        text:"Name",
        isMandatoryDesign:true
      },
      name:'name',
      description:'Enter your Name',
      placeholder:'Yo niga',
      icon:<User/>
    },
    {
      inputType:"email",
      error:null,
      label:{
        text:"Eame",
        isMandatoryDesign:true
      },
      name:'email',
      description:'Enter your Email',
      icon:<Mail/>
    },
    {
      inputType:"password",
      error:{
        type:{
          notEmpty:true,
          characterLimit:{min:3,max:10},
        }
      },
      label:{
        text:"Password",
        isMandatoryDesign:true
      },
      name:'password',
      description:'Enter your Password',
      icon:<LockIcon/>,
      confirmPassword:true
    },
    
  ]
  return (
    <div>
      <Form formInputs={formInput}>
        <FormHeader>
        <h2>User Profile</h2>
        <p style={{ color: "#555" }}>Edit your account details below</p>
      </FormHeader>
        </Form>
        </div>
  )
}

export default page