"use client"
import { usePathname } from 'next/navigation'
import React from 'react'

type Props = {}

const Header = (props: Props) => {
    const pathname  = usePathname().split('/')[4]

  return (
    <>
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h1 className="text-2xl font-semibold text-gray-900">{pathname[0].toUpperCase() + pathname.slice(1)}</h1>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <span>📅</span>
                Oct 18 - Nov 18
              </div>
            </div>
          </div>
        </div>
    </>
  )
}

export default Header