import Link from "next/link"
import { Plus, Search, Building2, Users, BookOpen, ChevronRight, Eye, Edit, Trash2 } from "lucide-react"
import { verifySession } from "@/app/lib/session"
import { redirect } from "next/navigation"
import prisma from "@/app/lib/prisma"

export default async function DepartmentsPage({ params }: { params: { univId: string } }) {
  // Verify session
  const user = await verifySession();
  if (!user) {
    redirect('/admin/login');
  }

  const { univId } = params;

  // Verify the university belongs to this admin
  const university = await prisma.univ.findFirst({
    where: {
      id: univId,
      adminId: user.id
    },
    select: {
      id: true,
      name: true,
      location: true
    }
  });

  if (!university) {
    redirect('/admin/all-university');
  }

  // Fetch departments for this university
  const departments = await prisma.department.findMany({
    where: {
      univId: univId,
      isActive: true
    },
    include: {
      courses: {
        where: { isActive: true },
        select: { id: true }
      },
      instructorTokens: {
        select: { id: true }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  });

  return (
    <div className="min-h-screen w-full bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-2">
              <div className="flex h-8 w-8 items-center justify-center rounded bg-green-600 text-white">
                <Building2 className="h-4 w-4" />
              </div>
              <span className="text-xl font-bold text-green-600">ExamPro</span>
            </div>
            <div className="flex items-center gap-4">
              <Link href={`/admin/${univId}/dashboard`} className="text-sm text-gray-600 hover:text-gray-900">
                Dashboard
              </Link>
              <Link href="/help" className="text-sm text-gray-600 hover:text-gray-900">
                Need help?
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Department Management</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Manage departments and courses for {university.name}
          </p>
        </div>

        {/* Search and Add Bar */}
        <div className="w-full mb-8 flex gap-4 justify-between items-center">
          <div className="relative w-[448px] max-w-[32rem]">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search departments..."
              className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
          </div>
          <Link 
            href={`/admin/${univId}/departments/create`}
            className="text-sm hover:bg-gray-100 py-3 !w-fit font-medium flex items-center border border-gray-300 rounded-lg text-gray-800 px-3"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Department
          </Link>
        </div>

        {/* Departments Grid */}
        <div className="grid w-full min-w-full items-center gap-4 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 mb-12">
          {departments.map((department) => (
            <DepartmentCard key={department.id} department={department} univId={univId} />
          ))}
        </div>

        {/* Empty State */}
        {departments.length === 0 && (
          <div className="text-center">
            <div className="bg-white rounded-lg border border-gray-200 p-6 max-w-md mx-auto">
              <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No departments found</h3>
              <p className="text-sm text-gray-600 mb-4">
                Create your first department to start organizing courses and managing instructor tokens.
              </p>
              <Link 
                href={`/admin/${univId}/departments/create`}
                className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors text-sm font-medium inline-block"
              >
                Create Department
              </Link>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}

function DepartmentCard({ department, univId }: { department: any, univId: string }) {
  const courseCount = department.courses.length;
  const tokenCount = department.instructorTokens.length;

  return (
    <div className="bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer group">
      <div className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
              <Building2 className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                {department.name}
              </h3>
              <p className="text-sm text-gray-500">{department.code}</p>
            </div>
          </div>
        </div>

        {department.description && (
          <p className="text-sm text-gray-600 mb-4 line-clamp-2">
            {department.description}
          </p>
        )}

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Courses:</span>
            <span className="text-sm font-medium text-gray-900">
              {courseCount}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Tokens:</span>
            <span className="text-sm font-medium text-gray-900">
              {tokenCount}
            </span>
          </div>
          
          {department.headOfDepartment && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Head:</span>
              <span className="text-sm font-medium text-gray-900">
                {department.headOfDepartment}
              </span>
            </div>
          )}
          
          {department.location && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Location:</span>
              <span className="text-sm font-medium text-gray-900">
                {department.location}
              </span>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="mt-6 grid grid-cols-2 gap-3">
          <Link
            href={`/admin/${univId}/departments/${department.id}`}
            className="flex items-center justify-center gap-2 bg-blue-50 text-blue-700 py-2 px-3 rounded-md hover:bg-blue-100 transition-colors text-sm font-medium group"
          >
            <Eye className="h-4 w-4" />
            View Details
            <ChevronRight className="h-3 w-3 group-hover:translate-x-0.5 transition-transform" />
          </Link>
          <Link
            href={`/admin/${univId}/departments/${department.id}/edit`}
            className="flex items-center justify-center gap-2 bg-gray-50 text-gray-700 py-2 px-3 rounded-md hover:bg-gray-100 transition-colors text-sm font-medium group"
          >
            <Edit className="h-4 w-4" />
            Edit
            <ChevronRight className="h-3 w-3 group-hover:translate-x-0.5 transition-transform" />
          </Link>
        </div>
      </div>
    </div>
  )
}
