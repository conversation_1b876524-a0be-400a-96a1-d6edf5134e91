'use client'

import { Menu } from "lucide-react"
import useSideBarStore from "../sidebar.store"

const MenuButton = () => {
    const setSidebarOpen = useSideBarStore(e => e.setOpen)
    return (
       <button onClick={() => setSidebarOpen(true)} className="lg:hidden p-2 rounded-md hover:bg-gray-100">
                <Menu className="h-5 w-5" />
              </button>
    )
}

export default MenuButton