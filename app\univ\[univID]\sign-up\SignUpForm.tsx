"use client"

import type React from "react"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from 'next/navigation'


interface FormData {
  name: string
  email: string
  password: string
  confirmPassword: string
  agreeToTerms: boolean
}

const  SignUpComponent = ({univID}:{univID:string}) => {
    
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
    agreeToTerms: false,
  })
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    const { name, email, password, confirmPassword, agreeToTerms } = formData

    // Validation
    if (!name || !email || !password || !confirmPassword) {
      setError('Please fill in all required fields.')
      return
    }
    if (password !== confirmPassword) {
      setError('Passwords do not match.')
      return
    }
    if (password.length < 8) {
      setError('Password must be at least 8 characters.')
      return
    }
    if (!agreeToTerms) {
      setError('You must agree to the terms and privacy policy.')
      return
    }

    setLoading(true)
    try {
      const res = await fetch('/api/auth/student/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ univID, name, email, password }),
      })
      if (!res.ok) {
        const err = await res.json()
        throw new Error(err.message || 'Registration failed')
      }
      // Redirect on success
      router.push(`/univ/${univID}/login`)
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex">
      {/* Left side - Sign up form */}
      <div className="flex-1 flex items-center justify-center p-8 bg-green-50">
        <div className="w-full max-w-md space-y-8">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-green-500 rounded-sm flex items-center justify-center">
              <div className="w-3 h-3 border border-white rounded-sm"></div>
            </div>
            <span className="text-xl font-semibold text-green-500">KING'S COMPANY</span>
          </div>

          {/* Header */}
          <div className="space-y-2">
            <h1 className="text-3xl font-bold text-green-500">Create your account</h1>
          </div>

          {/* Form */}
          {error && <div className="text-red-600 text-sm">{error}</div>}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Name<span className="text-red-500">*</span>
                </label>
                <input
                  id="name"
                  name="name"
                  type="text"
                  placeholder="Enter your name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full h-12 px-3 border border-gray-300 text-black rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  required
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-black mb-1">
                  Email<span className="text-red-500">*</span>
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter your email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full h-12 px-3 border border-gray-300 text-black rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  required
                />
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-black mb-1">
                  Password<span className="text-red-500">*</span>
                </label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  placeholder="Enter your password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="w-full h-12 px-3 border border-gray-300 text-black rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  required
                />
              </div>

              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-black mb-1">
                  Confirm Password<span className="text-red-500">*</span>
                </label>
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  placeholder="Confirm your password"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  className="w-full h-12 px-3 border border-gray-300 text-black rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  required
                />
              </div>
            </div>

            {/* Terms checkbox */}
            <div className="flex items-center space-x-2">
              <input
                id="terms"
                name="agreeToTerms"
                type="checkbox"
                checked={formData.agreeToTerms}
                onChange={handleInputChange}
                className="w-4 h-4 text-green-500 border-gray-300 rounded focus:ring-green-500"
              />
              <label htmlFor="terms" className="text-sm text-black">
                I agree to all Terms, Privacy Policy and Fees
              </label>
            </div>

            {/* Submit button */}
            <button
              type="submit"
              disabled={loading}
              className={`w-full h-12 bg-green-500 hover:bg-green-600 text-white font-medium rounded-md transition-colors ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Signing Up…' : 'Sign Up'}
            </button>
          </form>

          {/* Login link */}
          <p className="text-center text-sm text-black">
            Already have an account?{' '}
            <Link href={`/univ/${univID}/login`} className="text-green-500 hover:underline">
              Log in
            </Link>
          </p>
        </div>
      </div>

      {/* Right side - Hero image */}
      <div className="flex-1 relative bg-gray-100">
        <Image
          src={
            ''
          }
          alt="Modern living room with furniture"
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-black/40" />
        <div className="absolute top-0 translate-y-4/5 left-0 right-0 p-12 text-white">
  <h2 className="text-4xl font-bold mb-4 leading-tight">
    Smart Online Solutions
    <br />
    For Your Growing Examination
  </h2>
  <p className="text-lg mb-8 opacity-90 max-w-md">
    SmartOnlineSystem powers your brand with intuitive exam platforms, 
    data-driven insights, and seamless integrations to keep you ahead in a digital-first world.
  </p>
  <div className="flex space-x-4">
    <div className="flex items-center space-x-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2">
      <div className="w-5 h-5 bg-white rounded-full flex items-center justify-center">
        <svg className="w-3 h-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11H9v5h2V7zm0 7H9v2h2v-2z" clipRule="evenodd"/>
        </svg>
      </div>
      <span className="text-sm font-medium">99.9% Uptime Guarantee</span>
    </div>
    <div className="flex items-center space-x-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2">
      <div className="w-5 h-5 bg-white rounded-full flex items-center justify-center">
        <svg className="w-3 h-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
          <path d="M4 3h12a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1zm2 5h8v2H6V8zm0 4h8v2H6v-2z"/>
        </svg>
      </div>
      <span className="text-sm font-medium">Free Setup &amp; Support</span>
    </div>
  </div>
</div>
      </div>
    </div>
  )
}

export default SignUpComponent