import { SubscriptionService } from '@/lib/subscription-service'
import { PrismaClient } from '@prisma/client'

// Mock Prisma Client
jest.mock('@prisma/client')
const mockPrisma = new PrismaClient() as jest.Mocked<PrismaClient>

describe('SubscriptionService', () => {
  let subscriptionService: SubscriptionService

  beforeEach(() => {
    subscriptionService = new SubscriptionService()
    jest.clearAllMocks()
  })

  describe('createSubscription', () => {
    it('should create a subscription successfully', async () => {
      // Mock plan data
      const mockPlan = {
        id: 'plan_123',
        name: 'Premium Plan',
        price: 29.99,
        billing: 'monthly',
        currency: 'USD',
        features: ['Feature 1', 'Feature 2'],
        isPopular: false,
        savings: null,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      // Mock subscription data
      const mockSubscription = {
        id: 'sub_123',
        adminId: 'user_123',
        planId: 'plan_123',
        paymentId: 'pay_123',
        status: 'active',
        activeDate: new Date().toISOString(),
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      // Setup mocks
      ;(mockPrisma.plan.findUnique as jest.Mock).mockResolvedValue(mockPlan)
      ;(mockPrisma.subscription.create as jest.Mock).mockResolvedValue(mockSubscription)

      // Test the method
      const result = await subscriptionService.createSubscription({
        userId: 'user_123',
        planId: 'plan_123',
        paymentId: 'pay_123',
      })

      // Assertions
      expect(result).toBe('sub_123')
      expect(mockPrisma.plan.findUnique).toHaveBeenCalledWith({
        where: { id: 'plan_123' },
      })
      expect(mockPrisma.subscription.create).toHaveBeenCalledWith({
        data: {
          adminId: 'user_123',
          planId: 'plan_123',
          paymentId: 'pay_123',
          status: 'active',
          activeDate: expect.any(String),
        },
      })
    })

    it('should throw error when plan is not found', async () => {
      // Setup mock to return null (plan not found)
      ;(mockPrisma.plan.findUnique as jest.Mock).mockResolvedValue(null)

      // Test and expect error
      await expect(
        subscriptionService.createSubscription({
          userId: 'user_123',
          planId: 'nonexistent_plan',
          paymentId: 'pay_123',
        })
      ).rejects.toThrow('Plan with ID nonexistent_plan not found')
    })

    it('should handle database errors gracefully', async () => {
      // Setup mock to throw error
      ;(mockPrisma.plan.findUnique as jest.Mock).mockRejectedValue(
        new Error('Database connection failed')
      )

      // Test and expect error
      await expect(
        subscriptionService.createSubscription({
          userId: 'user_123',
          planId: 'plan_123',
          paymentId: 'pay_123',
        })
      ).rejects.toThrow('Database connection failed')
    })
  })

  describe('getUserSubscription', () => {
    it('should return user subscription when found', async () => {
      const mockSubscription = {
        id: 'sub_123',
        adminId: 'user_123',
        status: 'active',
        activeDate: '2024-01-01T00:00:00.000Z',
        plan: {
          name: 'Premium Plan',
          price: 29.99,
          billing: 'monthly',
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      ;(mockPrisma.subscription.findFirst as jest.Mock).mockResolvedValue(mockSubscription)

      const result = await subscriptionService.getUserSubscription('user_123')

      expect(result).toEqual({
        id: 'sub_123',
        status: 'active',
        plan: {
          name: 'Premium Plan',
          price: 29.99,
          billing: 'monthly',
        },
        startDate: new Date('2024-01-01T00:00:00.000Z'),
        endDate: expect.any(Date),
        daysRemaining: expect.any(Number),
        autoRenew: false,
      })
    })

    it('should return null when no subscription found', async () => {
      ;(mockPrisma.subscription.findFirst as jest.Mock).mockResolvedValue(null)

      const result = await subscriptionService.getUserSubscription('user_123')

      expect(result).toBeNull()
    })
  })

  describe('calculateEndDate', () => {
    it('should calculate monthly end date correctly', () => {
      const startDate = new Date('2024-01-01')
      const endDate = subscriptionService.calculateEndDate(startDate, 'monthly')
      
      expect(endDate.getMonth()).toBe(1) // February (0-indexed)
      expect(endDate.getDate()).toBe(1)
    })

    it('should calculate yearly end date correctly', () => {
      const startDate = new Date('2024-01-01')
      const endDate = subscriptionService.calculateEndDate(startDate, 'yearly')
      
      expect(endDate.getFullYear()).toBe(2025)
      expect(endDate.getMonth()).toBe(0) // January
      expect(endDate.getDate()).toBe(1)
    })
  })

  describe('calculateDaysRemaining', () => {
    it('should calculate days remaining correctly', () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 30)
      
      const daysRemaining = subscriptionService.calculateDaysRemaining(futureDate)
      
      expect(daysRemaining).toBe(30)
    })

    it('should return 0 for past dates', () => {
      const pastDate = new Date()
      pastDate.setDate(pastDate.getDate() - 5)
      
      const daysRemaining = subscriptionService.calculateDaysRemaining(pastDate)
      
      expect(daysRemaining).toBe(0)
    })
  })
})
