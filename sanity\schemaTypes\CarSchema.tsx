import { defineType } from 'sanity'

export default defineType({
  name: 'car',
  title: 'Car',
  type: 'document',
  fields: [
    {
      name: 'image',
      title: 'Image',
      type: 'image',
      options: { hotspot: true },
    },
    {
      name: 'name',
      title: 'Name',
      type: 'string',
    },
    {
      name: 'price',
      title: 'Price',
      type: 'number',
    },
    {
      name: 'location',
      title: 'Location',
      type: 'string',
    },
    {
      name: 'year',
      title: 'Year',
      type: 'number',
    },
    {
      name: 'mileage',
      title: 'Mileage',
      type: 'string',
    },
    {
      name: 'fuel',
      title: 'Fuel',
      type: 'string',
      options: {
        list: [
          { title: 'Gasoline', value: 'gasoline' },
          { title: 'Electric', value: 'electric' },
          { title: 'Super', value: 'super' },
          { title: 'Hybrid', value: 'hybrid' },
        ],
      },
    },
    {
      name: 'transmission',
      title: 'Transmission',
      type: 'string',
      options: {
        list: [
          { title: 'Automatic', value: 'automatic' },
          { title: 'Manual', value: 'manual' },
        ],
      },
    },
    {
      name: 'rating',
      title: 'Rating',
      type: 'number',
      validation: (Rule) => Rule.min(0).max(5),
    },
  ],
});
