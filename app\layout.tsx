import "./globals.css";
import { ThemeProvider } from "./component/theme-provider";
import { Metadata } from "next/dist/types";
import { Poppins,Inter } from 'next/font/google'
import {Toaster} from "sonner"
 
const poppins = Poppins({
  weight: ['100','200','300','400', '500', '600', '700','800','900'],
  subsets: ['latin'],
})
const inter = Inter({
  weight: ['100','200','300','400', '500', '600', '700','800','900'],
  subsets: ['latin'],
})
export const metadata: Metadata = {
  title: "Land Haven",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
      className={poppins.className}
      >
        <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
        <Toaster position="bottom-right" />
      </body>
    </html>
  );
}
