'use client'
import { formData } from '@/app/test4/type'
import React, { useEffect, useState, PropsWith<PERSON>hildren, ReactNode } from 'react'
import Input from './Input'
import { LockIcon } from 'lucide-react'

type Props = {}

// FormHeader.tsx

export function FormHeader({ children }: PropsWithChildren<{}>) {
  return <>{children}</>;
}
FormHeader.displayName = "FormHeader";


export function FormBody({ children }: PropsWithChildren<{}>) {
  return <>{children}</>;
}
FormBody.displayName = "FormBody";



export function FormFooter({ children }: PropsWithChildren<{}>) {
  return <>{children}</>;
}
FormFooter.displayName = "FormFooter";



const Form = ({formInputs,children,...props}: formData) => {
    const [formData, setFormData] = useState<Record<string, string | boolean>>(
    () => {
      const initial: Record<string, string | boolean> = {};
      formInputs.forEach((field) => {
        if (field.inputType === "textarea") {
          initial[field.name] = false;
        } else {
          initial[field.name] = "";
        }
        if (field.confirmPassword) {
          initial["confirmPassword"] = "";
        }
      });
      return initial;
    }
  );
    const [errors, setError] = useState<Record<string, string | boolean>>(
    () => {
      const initial: Record<string, string | boolean> = {};
      formInputs.forEach((field) => {
        initial[field.name] = false;
        if (field.confirmPassword) {
          initial["confirmPassword"] = false;
        }
      });
      return initial;
    }
  );



// 1) Flatten all children into an array of ReactNode
  const allChildren = React.Children.toArray(children);

  // 2) Partition them into header / body / footer
  
  let headerChildren: ReactNode[] = [];
  let footerChildren: ReactNode[] = [];
  let bodyChildren: ReactNode[] = [];

  allChildren.forEach((child) => {
    if(React.isValidElement(child)) console.log(child.type) 
    else{}
    if (
      React.isValidElement(child) &&
      (child.type === FormHeader || (child.type as any).displayName === "FormHeader")
    ) {
      headerChildren.push(child);
    } else if (
      React.isValidElement(child) &&
      (child.type === FormFooter || (child.type as any).displayName === "FormFooter")
    ) {
      footerChildren.push(child);
    } else {
      // Anything else (including FormBody, or raw <div>, <input>, etc.)
      bodyChildren.push(child);
    }
  });
console.log(headerChildren,bodyChildren,footerChildren)

  // 🔄 Generic change‐handler:
  const handleChange = (
    name: string,
    value: string | boolean
  ) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };
  useEffect(()=>{
    console.log(formData,errors)
  },[formData,errors])
  return (
    <form className='space-y-6'>
        <div className="">
            
            {headerChildren.length > 0 && (
            <div className='mb-6'>
            {headerChildren.map((el, i) => (
                <React.Fragment key={i}>{el}</React.Fragment>
            ))}
            </div>
        )}
        </div>
        {
            formInputs.map((inputData,i) =>{
                return(
                    <>
                        <div key={inputData.name+i.toString()}>
                        {
                            <>
                            {inputData.label && (
                                <label htmlFor={inputData.name} className='block text-sm font-medium text-brand-primary-foreground mb-1'>
                                    {inputData.label.text}  {inputData.label.isMandatoryDesign && (<span className='text-red-500'>*</span>)}
                                </label>
                                
                            )}
                            <Input error={inputData.error} setError={(e) => setError(prev => ({...prev,[inputData.name]:e}))} value={formData[inputData.name] as string} type={inputData.inputType} OnChange={(e) =>handleChange(inputData.name, e)} name={inputData.name}  icon={inputData.icon}  placeholder={inputData.placeholder}   />
                            </>
                        }
                    </div>
                    {
                        inputData.inputType == "password" && inputData.confirmPassword && (
                            <div className='' key={"confirmPassword"+i+1}>
                            <label className='block text-sm font-medium text-brand-primary-foreground mb-1'>Confirm Password <span className='text-red-500 text-sm'>*</span></label>
                            <Input type="password"  icon={<LockIcon/>} passwordValue={formData[inputData.name] as string} error={null} name="confirmPassword" OnChange={(e) =>handleChange("confirmPassword", e)}  placeholder="Confirm Password"  />
                            </div>
                        )
                    }
                    </>
                )
            })
        }
        {bodyChildren.length > 0 && (
        <div style={{ marginBottom: 24 }}>
          {bodyChildren.map((el, i) => (
            <React.Fragment key={i}>{el}</React.Fragment>
          ))}
        </div>
      )}
        {footerChildren.length > 0 && (
        <div className='mb-4'>
          {footerChildren.map((el, i) => (
            <React.Fragment key={i}>{el}</React.Fragment>
          ))}
        </div>
      )}
    </form>
  )
}

export default Form