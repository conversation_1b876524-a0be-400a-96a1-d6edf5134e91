import { ActivityService } from '@/lib/activity-service'
import { PrismaClient } from '@prisma/client'

// Mock Prisma Client
jest.mock('@prisma/client')
const mockPrisma = new PrismaClient() as jest.Mocked<PrismaClient>

describe('ActivityService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('logActivity', () => {
    it('should log activity successfully', async () => {
      const activityData = {
        type: 'user_registered',
        action: 'New user registered: <EMAIL>',
        userId: 'user_123',
        targetId: 'user_123',
        targetType: 'user',
        metadata: { email: '<EMAIL>' },
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
      }

      const mockActivity = {
        id: 'activity_123',
        ...activityData,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      ;(mockPrisma.activityLog.create as jest.Mock).mockResolvedValue(mockActivity)

      await ActivityService.logActivity(activityData)

      expect(mockPrisma.activityLog.create).toHaveBeenCalledWith({
        data: {
          type: 'user_registered',
          action: 'New user registered: <EMAIL>',
          userId: 'user_123',
          targetId: 'user_123',
          targetType: 'user',
          metadata: { email: '<EMAIL>' },
          ipAddress: '***********',
          userAgent: 'Mozilla/5.0',
        },
      })
    })

    it('should handle missing optional fields', async () => {
      const activityData = {
        type: 'system_maintenance',
        action: 'System maintenance started',
      }

      await ActivityService.logActivity(activityData)

      expect(mockPrisma.activityLog.create).toHaveBeenCalledWith({
        data: {
          type: 'system_maintenance',
          action: 'System maintenance started',
          userId: null,
          targetId: null,
          targetType: null,
          metadata: null,
          ipAddress: null,
          userAgent: null,
        },
      })
    })

    it('should not throw error when database operation fails', async () => {
      ;(mockPrisma.activityLog.create as jest.Mock).mockRejectedValue(
        new Error('Database error')
      )

      // Should not throw error
      await expect(
        ActivityService.logActivity({
          type: 'test',
          action: 'test action',
        })
      ).resolves.toBeUndefined()
    })
  })

  describe('getRecentActivities', () => {
    it('should return formatted activities', async () => {
      const mockActivities = [
        {
          id: 'activity_1',
          type: 'user_registered',
          action: 'User registered',
          userId: 'user_123',
          targetId: 'user_123',
          targetType: 'user',
          metadata: {},
          createdAt: new Date('2024-01-01T12:00:00Z'),
          user: {
            id: 'user_123',
            name: 'John Doe',
            email: '<EMAIL>',
          },
        },
        {
          id: 'activity_2',
          type: 'payment_completed',
          action: 'Payment completed',
          userId: null,
          targetId: 'payment_123',
          targetType: 'payment',
          metadata: {},
          createdAt: new Date('2024-01-01T11:00:00Z'),
          user: null,
        },
      ]

      ;(mockPrisma.activityLog.findMany as jest.Mock).mockResolvedValue(mockActivities)

      const result = await ActivityService.getRecentActivities(10)

      expect(result).toHaveLength(2)
      expect(result[0]).toEqual({
        id: 'activity_1',
        type: 'user_registered',
        action: 'User registered',
        user: 'John Doe',
        userId: 'user_123',
        targetId: 'user_123',
        targetType: 'user',
        metadata: {},
        time: expect.any(String),
        timestamp: new Date('2024-01-01T12:00:00Z'),
      })
      expect(result[1].user).toBe('System')
    })

    it('should return empty array on database error', async () => {
      ;(mockPrisma.activityLog.findMany as jest.Mock).mockRejectedValue(
        new Error('Database error')
      )

      const result = await ActivityService.getRecentActivities()

      expect(result).toEqual([])
    })
  })

  describe('getActivityStats', () => {
    it('should return activity statistics', async () => {
      const mockStats = [100, 10, 5, 15, 3] // totalActivities, todayActivities, userRegistrations, paymentActivities, subscriptionActivities

      ;(mockPrisma.activityLog.count as jest.Mock)
        .mockResolvedValueOnce(100) // total
        .mockResolvedValueOnce(10)  // today
        .mockResolvedValueOnce(5)   // registrations
        .mockResolvedValueOnce(15)  // payments
        .mockResolvedValueOnce(3)   // subscriptions

      const result = await ActivityService.getActivityStats()

      expect(result).toEqual({
        totalActivities: 100,
        todayActivities: 10,
        userRegistrations: 5,
        paymentActivities: 15,
        subscriptionActivities: 3,
      })
    })
  })

  describe('Helper methods', () => {
    it('should log user registration correctly', async () => {
      const logActivitySpy = jest.spyOn(ActivityService, 'logActivity')

      await ActivityService.logUserRegistration(
        'user_123',
        '<EMAIL>',
        '***********',
        'Mozilla/5.0'
      )

      expect(logActivitySpy).toHaveBeenCalledWith({
        type: ActivityService.ACTIVITY_TYPES.USER_REGISTERED,
        action: 'New user registered: <EMAIL>',
        userId: 'user_123',
        targetId: 'user_123',
        targetType: 'user',
        metadata: { email: '<EMAIL>' },
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
      })
    })

    it('should log payment completion correctly', async () => {
      const logActivitySpy = jest.spyOn(ActivityService, 'logActivity')

      await ActivityService.logPaymentCompleted(
        'payment_123',
        'user_123',
        29.99,
        'USD',
        '***********'
      )

      expect(logActivitySpy).toHaveBeenCalledWith({
        type: ActivityService.ACTIVITY_TYPES.PAYMENT_COMPLETED,
        action: 'Payment completed: 29.99 USD',
        userId: 'user_123',
        targetId: 'payment_123',
        targetType: 'payment',
        metadata: { amount: 29.99, currency: 'USD' },
        ipAddress: '***********',
      })
    })

    it('should log subscription creation correctly', async () => {
      const logActivitySpy = jest.spyOn(ActivityService, 'logActivity')

      await ActivityService.logSubscriptionCreated(
        'sub_123',
        'user_123',
        'Premium Plan'
      )

      expect(logActivitySpy).toHaveBeenCalledWith({
        type: ActivityService.ACTIVITY_TYPES.SUBSCRIPTION_CREATED,
        action: 'New subscription created: Premium Plan',
        userId: 'user_123',
        targetId: 'sub_123',
        targetType: 'subscription',
        metadata: { planName: 'Premium Plan' },
      })
    })
  })
})
