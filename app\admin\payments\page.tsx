import Link from "next/link"
import { Search, DollarSign, Calendar, CreditCard, CheckCircle, XCircle, Clock, AlertCircle, Filter, Download, Eye } from "lucide-react"
import prisma from "@/app/lib/prisma"
import { verifySession } from "@/app/lib/session";
import { redirect } from "next/navigation";

type PaymentType = {
  id: string;
  amount: number;
  currency: string;
  method: string;
  status: string;
  transactionId: string | null;
  email: string;
  createdAt: Date;
  updatedAt: Date;
  admin: {
    id: string;
    name: string;
    email: string;
  } | null;
  plan: {
    id: string;
    name: string;
    billing: string;
  } | null;
  subscription: {
    id: string;
    status: string;
  } | null;
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'completed':
      return <CheckCircle className="h-4 w-4 text-green-600" />
    case 'pending':
      return <Clock className="h-4 w-4 text-yellow-600" />
    case 'failed':
      return <XCircle className="h-4 w-4 text-red-600" />
    case 'cancelled':
      return <AlertCircle className="h-4 w-4 text-gray-600" />
    default:
      return <Clock className="h-4 w-4 text-gray-600" />
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'completed':
      return "bg-green-100 text-green-800"
    case 'pending':
      return "bg-yellow-100 text-yellow-800"
    case 'failed':
      return "bg-red-100 text-red-800"
    case 'cancelled':
      return "bg-gray-100 text-gray-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

const getMethodIcon = (method: string) => {
  switch (method.toLowerCase()) {
    case 'card':
    case 'credit_card':
      return <CreditCard className="h-4 w-4 text-blue-600" />
    case 'bank_transfer':
      return <DollarSign className="h-4 w-4 text-green-600" />
    default:
      return <DollarSign className="h-4 w-4 text-gray-600" />
  }
}

export default async function PaymentsPage({ searchParams }: { searchParams: { subscription?: string } }) {
  const user = await verifySession()
  if(!user) redirect('/login')
  
  // Build where clause based on search params
  const whereClause: any = {}
  if (searchParams.subscription) {
    whereClause.subscription = {
      id: searchParams.subscription
    }
  }
  
  // Fetch all payments with related data
  const payments = await prisma.payment.findMany({
    where: whereClause,
    select: {
      id: true,
      amount: true,
      currency: true,
      method: true,
      status: true,
      transactionId: true,
      email: true,
      createdAt: true,
      updatedAt: true,
      admin: {
        select: {
          id: true,
          name: true,
          email: true
        }
      },
      plan: {
        select: {
          id: true,
          name: true,
          billing: true
        }
      },
      subscription: {
        select: {
          id: true,
          status: true
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  })
  
  // Calculate statistics
  const stats = {
    total: payments.length,
    completed: payments.filter(p => p.status === 'completed').length,
    pending: payments.filter(p => p.status === 'pending').length,
    failed: payments.filter(p => p.status === 'failed').length,
    totalAmount: payments
      .filter(p => p.status === 'completed')
      .reduce((sum, p) => sum + p.amount, 0)
  }
  
  return (
    <div className="min-h-screen w-full bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-2">
              <div className="flex h-8 w-8 items-center justify-center rounded bg-green-600 text-white">
                <DollarSign className="h-4 w-4" />
              </div>
              <span className="text-xl font-bold text-green-600">ExamPro</span>
            </div>
            <div className="flex items-center gap-4">
              <Link href="/admin/subscriptions" className="text-sm text-gray-600 hover:text-gray-900">
                Subscriptions
              </Link>
              <Link href="/admin/all-university" className="text-sm text-gray-600 hover:text-gray-900">
                Universities
              </Link>
              <Link href="/help" className="text-sm text-gray-600 hover:text-gray-900">
                Need help?
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Payment Management</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Monitor and track all payment transactions across the platform
          </p>
          {searchParams.subscription && (
            <div className="mt-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                Filtered by Subscription: {searchParams.subscription}
              </span>
            </div>
          )}
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
                <DollarSign className="h-5 w-5 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Payments</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-green-100">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-gray-900">{stats.completed}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-yellow-100">
                <Clock className="h-5 w-5 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-gray-900">{stats.pending}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-red-100">
                <XCircle className="h-5 w-5 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Failed</p>
                <p className="text-2xl font-bold text-gray-900">{stats.failed}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-100">
                <DollarSign className="h-5 w-5 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">
                  {payments.length > 0 ? `${payments[0].currency} ${stats.totalAmount.toLocaleString()}` : 'XAF 0'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filter Bar */}
        <div className="w-full mb-8 flex gap-4 justify-between items-center">
          <div className="flex gap-4">
            <div className="relative w-[448px] max-w-[32rem]">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search by email, transaction ID, or amount..."
                className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              />
            </div>
            <button className="text-sm hover:bg-gray-100 py-3 !w-fit font-medium flex items-center border border-gray-300 rounded-lg text-gray-800 px-3">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </button>
          </div>
          <button className="text-sm hover:bg-gray-100 py-3 !w-fit font-medium flex items-center border border-gray-300 rounded-lg text-gray-800 px-3">
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
        </div>

        {/* Payments Table */}
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Payment Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Plan
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {payments.map((payment) => (
                  <tr key={payment.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getMethodIcon(payment.method)}
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">
                            {payment.transactionId || payment.id}
                          </div>
                          <div className="text-sm text-gray-500 capitalize">
                            {payment.method.replace('_', ' ')}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {payment.admin?.name || 'Unknown'}
                      </div>
                      <div className="text-sm text-gray-500">
                        {payment.email}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {payment.plan?.name || 'No Plan'}
                      </div>
                      {payment.plan && (
                        <div className="text-sm text-gray-500 capitalize">
                          {payment.plan.billing}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {payment.currency} {payment.amount.toLocaleString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
                        {getStatusIcon(payment.status)}
                        {payment.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {payment.createdAt.toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <Link
                        href={`/admin/payments/${payment.id}`}
                        className="text-blue-600 hover:text-blue-900 flex items-center gap-1"
                      >
                        <Eye className="h-4 w-4" />
                        View
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Empty State */}
        {payments.length === 0 && (
          <div className="text-center py-12">
            <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No payments found</h3>
            <p className="text-sm text-gray-600">
              {searchParams.subscription
                ? "No payments found for this subscription."
                : "There are no payment records in the system yet."
              }
            </p>
          </div>
        )}
      </main>
    </div>
  )
}
