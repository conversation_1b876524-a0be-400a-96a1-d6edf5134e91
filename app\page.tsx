import { User } from "lucide-react"
import Input, { ErrorType } from "./component/ui/Input"
import LoginPage from "./component/ui/LoginPage";
import Link from "next/link";



interface Props {
    
}

const Home = (props: Props) => {
    const error:{
        message?: string[];
        type?: ErrorType;
    } | null = {
        type: {
            notEmpty: true,
            characterLimit: { min: 3, max: 10 },
            NoSpecailcharaterType: "NoSpecialCharacter"
        }
    }

    return (
        <div className="w-full h-screen flex justify-center items-center gap-4">
            <Link href="/admin/login"><button  className="px-2 py-2 bg-green-600  text-white rounded">Adminstrator</button></Link>
            <button  className="px-2 py-2 bg-blue-600  text-white rounded">University</button>
        </div>
    )
}

export default Home;
