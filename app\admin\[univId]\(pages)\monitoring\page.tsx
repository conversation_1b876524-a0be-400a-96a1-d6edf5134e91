"use client"

import { useState } from "react"
import {
  Search,
  Eye,
  StopCircle,
  AlertTriangle,
  Clock,
  Users,
  Play,
  Pause,
  MoreHorizontal,
  Calendar,
  University,
  User,
  Activity,
  RefreshCw,
} from "lucide-react"

export default function LiveExamMonitoring() {
  const [searchTerm, setSearchTerm] = useState("")
  const [instructorFilter, setInstructorFilter] = useState("all")
  const [universityFilter, setUniversityFilter] = useState("all")
  const [autoRefresh, setAutoRefresh] = useState(true)

  const activeExams = [
    {
      id: 1,
      title: "Advanced Calculus Midterm",
      instructor: "Dr. <PERSON>",
      university: "MIT",
      startTime: "2024-01-15T09:00:00",
      duration: 120,
      participants: 45,
      activeParticipants: 42,
      status: "in_progress",
      suspiciousActivity: 2,
      flaggedStudents: ["<PERSON>", "<PERSON>"],
    },
    {
      id: 2,
      title: "Physics 101 Final Exam",
      instructor: "Dr. <PERSON>",
      university: "Harvard",
      startTime: "2024-01-15T10:30:00",
      duration: 180,
      participants: 67,
      activeParticipants: 65,
      status: "in_progress",
      suspiciousActivity: 0,
      flaggedStudents: [],
    },
    {
      id: 3,
      title: "Computer Science Quiz",
      instructor: "Prof. Lisa Chen",
      university: "Stanford",
      startTime: "2024-01-15T14:00:00",
      duration: 60,
      participants: 23,
      activeParticipants: 23,
      status: "starting",
      suspiciousActivity: 0,
      flaggedStudents: [],
    },
    {
      id: 4,
      title: "Chemistry Lab Assessment",
      instructor: "Dr. Robert Davis",
      university: "UC Berkeley",
      startTime: "2024-01-15T08:00:00",
      duration: 90,
      participants: 34,
      activeParticipants: 0,
      status: "completed",
      suspiciousActivity: 1,
      flaggedStudents: ["Mike Chen"],
    },
  ]

  const suspiciousActivities = [
    {
      id: 1,
      examId: 1,
      examTitle: "Advanced Calculus Midterm",
      student: "John Doe",
      activity: "Multiple login attempts from different IPs",
      timestamp: "2024-01-15T10:15:00",
      severity: "high",
    },
    {
      id: 2,
      examId: 1,
      examTitle: "Advanced Calculus Midterm",
      student: "Jane Smith",
      activity: "Unusual mouse movement patterns detected",
      timestamp: "2024-01-15T10:20:00",
      severity: "medium",
    },
    {
      id: 3,
      examId: 4,
      examTitle: "Chemistry Lab Assessment",
      student: "Mike Chen",
      activity: "Late join after 30 minutes",
      timestamp: "2024-01-15T08:30:00",
      severity: "low",
    },
  ]

  const instructors = ["Dr. Sarah Wilson", "Dr. Michael Johnson", "Prof. Lisa Chen", "Dr. Robert Davis"]
  const universities = ["MIT", "Harvard", "Stanford", "UC Berkeley"]

  const filteredExams = activeExams.filter((exam) => {
    const matchesSearch =
      exam.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      exam.instructor.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesInstructor = instructorFilter === "all" || exam.instructor === instructorFilter
    const matchesUniversity = universityFilter === "all" || exam.university === universityFilter
    return matchesSearch && matchesInstructor && matchesUniversity
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "in_progress":
        return "bg-green-100 text-green-800"
      case "starting":
        return "bg-blue-100 text-blue-800"
      case "completed":
        return "bg-gray-100 text-gray-800"
      case "paused":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "in_progress":
        return <Play className="h-4 w-4 text-green-600" />
      case "starting":
        return <Clock className="h-4 w-4 text-blue-600" />
      case "completed":
        return <StopCircle className="h-4 w-4 text-gray-600" />
      case "paused":
        return <Pause className="h-4 w-4 text-yellow-600" />
      default:
        return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "high":
        return "bg-red-100 text-red-800"
      case "medium":
        return "bg-yellow-100 text-yellow-800"
      case "low":
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getTimeRemaining = (startTime: string, duration: number) => {
    const start = new Date(startTime)
    const end = new Date(start.getTime() + duration * 60000)
    const now = new Date()
    const remaining = end.getTime() - now.getTime()

    if (remaining <= 0) return "Completed"

    const hours = Math.floor(remaining / (1000 * 60 * 60))
    const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60))

    return `${hours}h ${minutes}m remaining`
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="p-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Live Exam Monitoring</h1>
              <p className="text-gray-600">Monitor active exams and detect suspicious activities in real-time</p>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setAutoRefresh(!autoRefresh)}
                className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium ${
                  autoRefresh ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                }`}
              >
                <RefreshCw className={`h-4 w-4 ${autoRefresh ? "animate-spin" : ""}`} />
                Auto Refresh {autoRefresh ? "On" : "Off"}
              </button>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search exams..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-full sm:w-80"
              />
            </div>

            <select
              value={instructorFilter}
              onChange={(e) => setInstructorFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Instructors</option>
              {instructors.map((instructor) => (
                <option key={instructor} value={instructor}>
                  {instructor}
                </option>
              ))}
            </select>

            <select
              value={universityFilter}
              onChange={(e) => setUniversityFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Universities</option>
              {universities.map((uni) => (
                <option key={uni} value={uni}>
                  {uni}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          {/* Active Exams */}
          <div className="xl:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border">
              <div className="p-4 border-b">
                <h2 className="text-lg font-semibold text-gray-900">Active Exams</h2>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b">
                    <tr>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Exam</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Participants</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Time</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {filteredExams.map((exam) => (
                      <tr key={exam.id} className="hover:bg-gray-50">
                        <td className="py-4 px-4">
                          <div>
                            <p className="font-medium text-gray-900">{exam.title}</p>
                            <div className="flex items-center gap-4 mt-1 text-sm text-gray-500">
                              <span className="flex items-center gap-1">
                                <User className="h-3 w-3" />
                                {exam.instructor}
                              </span>
                              <span className="flex items-center gap-1">
                                <University className="h-3 w-3" />
                                {exam.university}
                              </span>
                            </div>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-900">
                              {exam.activeParticipants}/{exam.participants}
                            </span>
                            {exam.suspiciousActivity > 0 && (
                              <span className="flex items-center gap-1 text-xs text-red-600">
                                <AlertTriangle className="h-3 w-3" />
                                {exam.suspiciousActivity}
                              </span>
                            )}
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <div className="text-sm">
                            <p className="text-gray-900 flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {new Date(exam.startTime).toLocaleTimeString()}
                            </p>
                            <p className="text-gray-500">{getTimeRemaining(exam.startTime, exam.duration)}</p>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <span
                            className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(exam.status)}`}
                          >
                            {getStatusIcon(exam.status)}
                            {exam.status.replace("_", " ").charAt(0).toUpperCase() +
                              exam.status.replace("_", " ").slice(1)}
                          </span>
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex items-center gap-2">
                            <button className="p-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded">
                              <Eye className="h-4 w-4" />
                            </button>
                            <button className="p-1 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded">
                              <StopCircle className="h-4 w-4" />
                            </button>
                            <button className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded">
                              <MoreHorizontal className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Suspicious Activity */}
          <div>
            <div className="bg-white rounded-lg shadow-sm border">
              <div className="p-4 border-b">
                <h2 className="text-lg font-semibold text-gray-900">Suspicious Activity</h2>
              </div>
              <div className="p-4 space-y-4 max-h-96 overflow-y-auto">
                {suspiciousActivities.map((activity) => (
                  <div key={activity.id} className="border border-gray-200 rounded-lg p-3">
                    <div className="flex items-start justify-between mb-2">
                      <span
                        className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(activity.severity)}`}
                      >
                        <AlertTriangle className="h-3 w-3" />
                        {activity.severity.toUpperCase()}
                      </span>
                      <span className="text-xs text-gray-500">{new Date(activity.timestamp).toLocaleTimeString()}</span>
                    </div>
                    <p className="text-sm font-medium text-gray-900 mb-1">{activity.student}</p>
                    <p className="text-sm text-gray-600 mb-2">{activity.activity}</p>
                    <p className="text-xs text-gray-500">{activity.examTitle}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
