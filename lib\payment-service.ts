import { PrismaClient } from '@prisma/client';
import { FlutterwaveService } from './payment-gateways/flutterwave';
import prisma from '@/app/lib/prisma';



export interface PaymentRequest {
  userId?: string;
  email: string;
  amount: number;
  currency: string;
  method: 'flutterwave' | 'card' | 'mobilemoney' | 'banktransfer';
  planId: string;
  phoneNumber?: string;
  orderId: string;
  customerName?: string;
}

export interface PaymentResult {
  success: boolean;
  paymentId?: string;
  paymentUrl?: string;
  transactionId?: string;
  error?: string;
}

export class PaymentService {
  private flutterwaveService: FlutterwaveService;

  constructor() {
    this.flutterwaveService = new FlutterwaveService();
  }

  async initiatePayment(request: PaymentRequest): Promise<PaymentResult> {
    try {
      // Generate transaction reference
      const txRef = this.flutterwaveService.generateTxRef('sub');

      // Create payment record in database (using current schema)
      const payment = await prisma.payment.create({
        data: {
          email: request.email,
          amount: request.amount,
          method: request.method,
          token: txRef,
          planId:request.planId
        },
      });

      // Validate phone number if provided
      if (request.phoneNumber && !this.flutterwaveService.validatePhoneNumber(request.phoneNumber)) {
        return {
          success: false,
          error: 'Invalid phone number format',
        };
      }

      // Initiate payment with Flutterwave
      const result = await this.flutterwaveService.initiatePayment(
        request.amount,
        request.email,
        request.phoneNumber || '',
        txRef,
        request.currency,
        request.customerName
      );

      if (result.success) {
        // Update payment record with processing status
        try {
          await prisma.payment.update({
            where: { id: payment.id },
            data: {
              status: 'processing',
            },
          });
        } catch (error) {
          console.error('Failed to update payment status:', error);
        }
      }

      return {
        success: result.success,
        paymentId: payment.id,
        paymentUrl: result.paymentUrl,
        transactionId: txRef,
        error: result.error,
      };
    } catch (error: any) {
      console.error('Payment initiation failed:', error);
      return {
        success: false,
        error: error.message || 'Payment initiation failed',
      };
    }
  }

  async verifyPayment(txRef: string): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const verification = await this.flutterwaveService.verifyPaymentByTxRef(txRef);

      if (verification.status === 'success' && verification.data?.status === 'successful') {
        return {
          success: true,
          data: verification.data,
        };
      }

      return {
        success: false,
        error: verification.message || 'Payment verification failed',
      };
    } catch (error: any) {
      console.error('Payment verification failed:', error);
      return {
        success: false,
        error: error.message || 'Payment verification failed',
      };
    }
  }

  async confirmPayment(paymentId: string, txRef?: string): Promise<boolean> {
    try {
      const payment = await prisma.payment.findUnique({
        where: { id: paymentId },
      });

      if (!payment) {
        return false;
      }

      // Use provided txRef or payment token
      const transactionRef = txRef || payment.token;

      // Verify payment with Flutterwave
      const verification = await this.verifyPayment(transactionRef);

      if (verification.success) {
        await prisma.payment.update({
          where: { id: paymentId },
          data: { status: 'completed' },
        });
        return true;
      }

      return false;
    } catch (error) {
      console.error('Payment confirmation failed:', error);
      return false;
    }
  }
}
