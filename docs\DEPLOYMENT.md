# SmartOnline Deployment Guide

## Overview

This guide covers the deployment process for SmartOnline across different environments and platforms.

## Prerequisites

- Node.js 18+ installed
- PostgreSQL database
- Domain name (for production)
- SSL certificate (for production)
- Payment gateway accounts
- Email service credentials

## Environment Configuration

### Environment Variables

Create environment files for each deployment stage:

#### `.env.local` (Development)
```env
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/smartonline_dev"

# Authentication
JWT_SECRET="your-development-jwt-secret"
NEXTAUTH_URL="http://localhost:3000"

# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Sanity CMS
NEXT_PUBLIC_SANITY_PROJECT_ID="your-project-id"
NEXT_PUBLIC_SANITY_DATASET="development"
SANITY_API_TOKEN="your-sanity-token"

# Payment Gateways (Test Keys)
FLUTTERWAVE_PUBLIC_KEY="FLWPUBK_TEST-xxxxx"
FLUTTERWAVE_SECRET_KEY="FLWSECK_TEST-xxxxx"
PAYPAL_CLIENT_ID="your-paypal-sandbox-client-id"
PAYPAL_CLIENT_SECRET="your-paypal-sandbox-secret"

# Feature Flags
ENABLE_REGISTRATION="true"
ENABLE_PAYMENTS="true"
DEBUG_MODE="true"
```

#### `.env.production` (Production)
```env
# Database
DATABASE_URL="***************************************/smartonline_prod"

# Authentication
JWT_SECRET="your-super-secure-production-jwt-secret"
NEXTAUTH_URL="https://smartonline.com"

# Email Configuration
SMTP_HOST="smtp.sendgrid.net"
SMTP_PORT="587"
SMTP_USER="apikey"
SMTP_PASS="your-sendgrid-api-key"

# Sanity CMS
NEXT_PUBLIC_SANITY_PROJECT_ID="your-project-id"
NEXT_PUBLIC_SANITY_DATASET="production"
SANITY_API_TOKEN="your-production-sanity-token"

# Payment Gateways (Live Keys)
FLUTTERWAVE_PUBLIC_KEY="FLWPUBK-xxxxx"
FLUTTERWAVE_SECRET_KEY="FLWSECK-xxxxx"
PAYPAL_CLIENT_ID="your-paypal-live-client-id"
PAYPAL_CLIENT_SECRET="your-paypal-live-secret"

# Feature Flags
ENABLE_REGISTRATION="true"
ENABLE_PAYMENTS="true"
DEBUG_MODE="false"
```

## Database Setup

### Development Database
```bash
# Install PostgreSQL locally
# Create database
createdb smartonline_dev

# Run migrations
npx prisma generate
npx prisma db push

# Seed database (optional)
npx prisma db seed
```

### Production Database
```bash
# Set up production database
# Apply migrations
npx prisma migrate deploy

# Generate Prisma client
npx prisma generate
```

## Build Process

### Development Build
```bash
npm install
npm run dev
```

### Production Build
```bash
# Install dependencies
npm ci --only=production

# Build application
npm run build

# Start production server
npm start
```

## Deployment Platforms

### 1. Vercel Deployment (Recommended)

#### Setup
```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod
```

#### Vercel Configuration (`vercel.json`)
```json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/next"
    }
  ],
  "env": {
    "DATABASE_URL": "@database-url",
    "JWT_SECRET": "@jwt-secret"
  },
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  }
}
```

### 2. Docker Deployment

#### Dockerfile
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build application
RUN npm run build

# Production image
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

#### Docker Compose
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=**************************************/smartonline
    depends_on:
      - db
    volumes:
      - ./uploads:/app/uploads

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: smartonline
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app

volumes:
  postgres_data:
```

### 3. AWS Deployment

#### Using AWS Amplify
```bash
# Install Amplify CLI
npm install -g @aws-amplify/cli

# Initialize Amplify
amplify init

# Add hosting
amplify add hosting

# Deploy
amplify publish
```

#### Using AWS ECS
```yaml
# task-definition.json
{
  "family": "smartonline",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "smartonline",
      "image": "your-account.dkr.ecr.region.amazonaws.com/smartonline:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "DATABASE_URL",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:database-url"
        }
      ]
    }
  ]
}
```

## SSL Configuration

### Let's Encrypt with Nginx
```nginx
server {
    listen 80;
    server_name smartonline.com www.smartonline.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name smartonline.com www.smartonline.com;

    ssl_certificate /etc/letsencrypt/live/smartonline.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/smartonline.com/privkey.pem;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Monitoring and Logging

### Application Monitoring
```javascript
// lib/monitoring.js
import { createLogger, format, transports } from 'winston';

export const logger = createLogger({
  level: 'info',
  format: format.combine(
    format.timestamp(),
    format.errors({ stack: true }),
    format.json()
  ),
  defaultMeta: { service: 'smartonline' },
  transports: [
    new transports.File({ filename: 'logs/error.log', level: 'error' }),
    new transports.File({ filename: 'logs/combined.log' }),
  ],
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new transports.Console({
    format: format.simple()
  }));
}
```

### Health Check Endpoint
```typescript
// app/api/health/route.ts
import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    // Check database connection
    await prisma.$queryRaw`SELECT 1`;
    
    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'connected',
        api: 'operational'
      }
    });
  } catch (error) {
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    }, { status: 503 });
  }
}
```

## Backup Strategy

### Database Backup
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"
DB_NAME="smartonline"

# Create backup
pg_dump $DATABASE_URL > $BACKUP_DIR/backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/backup_$DATE.sql

# Remove backups older than 30 days
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +30 -delete

echo "Backup completed: backup_$DATE.sql.gz"
```

### File Backup
```bash
#!/bin/bash
# file-backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"
APP_DIR="/app"

# Backup uploads and important files
tar -czf $BACKUP_DIR/files_$DATE.tar.gz \
  $APP_DIR/uploads \
  $APP_DIR/.env.production \
  $APP_DIR/prisma

echo "File backup completed: files_$DATE.tar.gz"
```

## Performance Optimization

### CDN Configuration
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['cdn.smartonline.com'],
    loader: 'cloudinary',
    path: 'https://res.cloudinary.com/smartonline/',
  },
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Cache-Control', value: 'public, max-age=3600' },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
```

### Database Optimization
```sql
-- Create indexes for better performance
CREATE INDEX idx_student_univ_id ON "Student"("univId");
CREATE INDEX idx_payment_status ON "Payment"("status");
CREATE INDEX idx_subscription_admin_id ON "Subscription"("adminId");
CREATE INDEX idx_department_univ_id ON "Department"("univId");
```

## Security Checklist

- [ ] Environment variables secured
- [ ] Database credentials encrypted
- [ ] SSL certificates installed
- [ ] CORS properly configured
- [ ] Rate limiting implemented
- [ ] Input validation in place
- [ ] SQL injection protection
- [ ] XSS protection enabled
- [ ] CSRF tokens implemented
- [ ] Security headers configured

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
```bash
# Check database connectivity
npx prisma db pull

# Reset database (development only)
npx prisma migrate reset
```

2. **Build Failures**
```bash
# Clear Next.js cache
rm -rf .next

# Clear node modules
rm -rf node_modules package-lock.json
npm install
```

3. **Environment Variable Issues**
```bash
# Verify environment variables
node -e "console.log(process.env.DATABASE_URL)"
```

### Logs Analysis
```bash
# View application logs
tail -f logs/combined.log

# View error logs only
tail -f logs/error.log | grep ERROR

# View real-time logs with Docker
docker logs -f container_name
```

## Rollback Procedures

### Application Rollback
```bash
# Rollback to previous version
vercel rollback

# Or with Docker
docker pull smartonline:previous-tag
docker stop smartonline-current
docker run -d --name smartonline-rollback smartonline:previous-tag
```

### Database Rollback
```bash
# Rollback database migration
npx prisma migrate reset --force

# Restore from backup
psql $DATABASE_URL < backup_file.sql
