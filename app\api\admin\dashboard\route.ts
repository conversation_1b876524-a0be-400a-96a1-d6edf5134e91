import { NextRequest, NextResponse } from 'next/server';
import { verifySession } from '@/app/lib/session';

export async function GET(request: NextRequest) {
  try {
    // Verify admin session
    const user = await verifySession();
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Fetch dashboard data using the service
    const dashboardData = await DashboardService.getDashboardData();

    return NextResponse.json({
      success: true,
      data: dashboardData
    });

  } catch (error: any) {
    console.error('Dashboard data fetch failed:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to fetch dashboard data' },
      { status: 500 }
    );
  }
}
