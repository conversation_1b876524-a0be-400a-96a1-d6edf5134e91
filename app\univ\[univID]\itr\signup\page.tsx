import React from 'react'
import SignupPage from './Form'
import prisma from '@/app/lib/prisma'

const page = async ({params}:{params:Promise<{univID:string}>}) => {
    const {univID} = await params
    const universityName = await prisma.univ.findUnique({where:{id:univID},select:{name:true}})
  return (
    <SignupPage univID={univID} univName={universityName?.name || 'Ict university'} />
  )
}

export default page
