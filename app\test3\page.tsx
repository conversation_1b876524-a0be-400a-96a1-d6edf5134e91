"use client"
import { useState } from "react"
import { toast } from "sonner";

const Test3 = () => {
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, password }),
        });
        if (response.status === 200) {
            const data = await response.json();
            toast.success(data.message);
            setEmail('');
            setPassword('');
        } else {
            const errorData = await response.json();
            toast.error(errorData.message || "login failed");
        }
    };

    return (
        <div className="w-full h-screen bg-violet-100 flex justify-center items-center">
            <form onSubmit={handleSubmit} className="w-[400px] p-1 bg-blue-300 rounded-md">
                <div>
                </div>
                <div className="mt-4">
                    <label className="block mb-2 text-sm font-medium text-black dark:text-black">Email</label>
                    <input
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        type="email"
                        className="bg-[#fdfdfd] px-2 py-1 w-full outline-none text-black border-[1px] border-violet-400 rounded"
                    />
                </div>
                <div className="mt-4">
                    <label className="block mb-2 text-sm font-medium text-black dark:text-black"> password</label>
                    <input
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        type="password"
                        className="bg-[#f9f7f7] px-2 py-1 w-full outline-none text-black border-[1px] border-violet-400 rounded"
                    />
                </div>
                <div className="mt-4">
                    <button type="submit" className="w-full mt-4 bg-violet-500 text-white px-4 py-2 rounded hover:bg-violet-600 transition duration-200">LogIn</button>
                </div>
            </form>
        </div>
    );
};

export default Test3;