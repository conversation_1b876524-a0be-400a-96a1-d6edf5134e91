import { QuestionService } from '@/lib/question-service'
import { PrismaClient } from '@prisma/client'

// Mock Prisma Client
jest.mock('@prisma/client')
const mockPrisma = new PrismaClient() as jest.Mocked<PrismaClient>

describe('QuestionService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('createQuestion', () => {
    it('should create a question successfully', async () => {
      const questionData = {
        title: 'Test Question',
        text: 'What is 2 + 2?',
        type: 'multiple-choice' as const,
        subject: 'Mathematics',
        difficulty: 'Easy' as const,
        tags: ['arithmetic', 'basic'],
        options: ['2', '3', '4', '5'],
        correctAnswer: 2,
        univId: 'univ_123',
        createdBy: 'admin_123',
      }

      const mockQuestion = {
        id: 'question_123',
        ...questionData,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      ;(mockPrisma.question.create as jest.Mock).mockResolvedValue(mockQuestion)

      const result = await QuestionService.createQuestion(questionData)

      expect(result).toBe('question_123')
      expect(mockPrisma.question.create).toHaveBeenCalledWith({
        data: {
          title: 'Test Question',
          text: 'What is 2 + 2?',
          type: 'multiple-choice',
          subject: 'Mathematics',
          difficulty: 'Easy',
          tags: ['arithmetic', 'basic'],
          options: ['2', '3', '4', '5'],
          correctAnswer: 2,
          wordLimit: undefined,
          courseId: undefined,
          departmentId: undefined,
          univId: 'univ_123',
          createdBy: 'admin_123',
        },
      })
    })

    it('should handle creation errors', async () => {
      const questionData = {
        title: 'Test Question',
        text: 'What is 2 + 2?',
        type: 'multiple-choice' as const,
        subject: 'Mathematics',
        difficulty: 'Easy' as const,
        tags: ['arithmetic'],
        univId: 'univ_123',
        createdBy: 'admin_123',
      }

      ;(mockPrisma.question.create as jest.Mock).mockRejectedValue(
        new Error('Database error')
      )

      await expect(QuestionService.createQuestion(questionData)).rejects.toThrow(
        'Database error'
      )
    })
  })

  describe('getQuestions', () => {
    it('should return paginated questions with filters', async () => {
      const mockQuestions = [
        {
          id: 'q1',
          title: 'Question 1',
          text: 'Test question 1',
          type: 'multiple-choice',
          subject: 'Math',
          difficulty: 'Easy',
          tags: ['test'],
          options: ['A', 'B', 'C', 'D'],
          correctAnswer: 0,
          wordLimit: null,
          course: { id: 'c1', name: 'Course 1', code: 'C001' },
          department: { id: 'd1', name: 'Dept 1', code: 'D001' },
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-01'),
        },
      ]

      ;(mockPrisma.question.count as jest.Mock).mockResolvedValue(1)
      ;(mockPrisma.question.findMany as jest.Mock).mockResolvedValue(mockQuestions)

      const filter = {
        univId: 'univ_123',
        subject: 'Math',
        difficulty: 'Easy',
      }

      const result = await QuestionService.getQuestions(filter, 1, 20)

      expect(result).toEqual({
        questions: [
          {
            id: 'q1',
            title: 'Question 1',
            text: 'Test question 1',
            type: 'multiple-choice',
            subject: 'Math',
            difficulty: 'Easy',
            tags: ['test'],
            options: ['A', 'B', 'C', 'D'],
            correctAnswer: 0,
            wordLimit: null,
            course: { id: 'c1', name: 'Course 1', code: 'C001' },
            department: { id: 'd1', name: 'Dept 1', code: 'D001' },
            lastEdited: '2024-01-01',
            createdAt: new Date('2024-01-01'),
            updatedAt: new Date('2024-01-01'),
          },
        ],
        total: 1,
        totalPages: 1,
        currentPage: 1,
      })

      expect(mockPrisma.question.findMany).toHaveBeenCalledWith({
        where: {
          univId: 'univ_123',
          isActive: true,
          subject: 'Math',
          difficulty: 'Easy',
        },
        include: {
          course: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
          department: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: 0,
        take: 20,
      })
    })

    it('should handle search queries', async () => {
      ;(mockPrisma.question.count as jest.Mock).mockResolvedValue(0)
      ;(mockPrisma.question.findMany as jest.Mock).mockResolvedValue([])

      const filter = {
        univId: 'univ_123',
        search: 'test query',
      }

      await QuestionService.getQuestions(filter)

      expect(mockPrisma.question.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: [
              { title: { contains: 'test query', mode: 'insensitive' } },
              { text: { contains: 'test query', mode: 'insensitive' } },
              { subject: { contains: 'test query', mode: 'insensitive' } },
            ],
          }),
        })
      )
    })
  })

  describe('getQuestionById', () => {
    it('should return a question by ID', async () => {
      const mockQuestion = {
        id: 'q1',
        title: 'Question 1',
        text: 'Test question',
        type: 'essay',
        subject: 'English',
        difficulty: 'Medium',
        tags: ['writing'],
        options: [],
        correctAnswer: null,
        wordLimit: 500,
        course: null,
        department: null,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-02'),
      }

      ;(mockPrisma.question.findFirst as jest.Mock).mockResolvedValue(mockQuestion)

      const result = await QuestionService.getQuestionById('q1', 'univ_123')

      expect(result).toEqual({
        id: 'q1',
        title: 'Question 1',
        text: 'Test question',
        type: 'essay',
        subject: 'English',
        difficulty: 'Medium',
        tags: ['writing'],
        options: [],
        correctAnswer: null,
        wordLimit: 500,
        course: null,
        department: null,
        lastEdited: '2024-01-02',
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-02'),
      })
    })

    it('should return null if question not found', async () => {
      ;(mockPrisma.question.findFirst as jest.Mock).mockResolvedValue(null)

      const result = await QuestionService.getQuestionById('nonexistent', 'univ_123')

      expect(result).toBeNull()
    })
  })

  describe('updateQuestion', () => {
    it('should update a question successfully', async () => {
      ;(mockPrisma.question.updateMany as jest.Mock).mockResolvedValue({ count: 1 })

      const updateData = {
        title: 'Updated Question',
        difficulty: 'Hard' as const,
      }

      const result = await QuestionService.updateQuestion('q1', 'univ_123', updateData)

      expect(result).toBe(true)
      expect(mockPrisma.question.updateMany).toHaveBeenCalledWith({
        where: {
          id: 'q1',
          univId: 'univ_123',
          isActive: true,
        },
        data: {
          ...updateData,
          updatedAt: expect.any(Date),
        },
      })
    })
  })

  describe('deleteQuestion', () => {
    it('should soft delete a question', async () => {
      ;(mockPrisma.question.updateMany as jest.Mock).mockResolvedValue({ count: 1 })

      const result = await QuestionService.deleteQuestion('q1', 'univ_123')

      expect(result).toBe(true)
      expect(mockPrisma.question.updateMany).toHaveBeenCalledWith({
        where: {
          id: 'q1',
          univId: 'univ_123',
          isActive: true,
        },
        data: {
          isActive: false,
          updatedAt: expect.any(Date),
        },
      })
    })
  })

  describe('getQuestionStats', () => {
    it('should return question statistics', async () => {
      const mockQuestions = [
        { subject: 'Math', difficulty: 'Easy', type: 'multiple-choice' },
        { subject: 'Math', difficulty: 'Hard', type: 'essay' },
        { subject: 'Science', difficulty: 'Easy', type: 'multiple-choice' },
      ]

      ;(mockPrisma.question.findMany as jest.Mock).mockResolvedValue(mockQuestions)

      const result = await QuestionService.getQuestionStats('univ_123')

      expect(result).toEqual({
        total: 3,
        bySubject: {
          Math: 2,
          Science: 1,
        },
        byDifficulty: {
          Easy: 2,
          Hard: 1,
        },
        byType: {
          'multiple-choice': 2,
          essay: 1,
        },
      })
    })
  })
})
