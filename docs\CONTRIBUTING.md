# Contributing to SmartOnline

Thank you for your interest in contributing to SmartOnline! This document provides guidelines and instructions for contributing to the project.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Process](#development-process)
- [Pull Request Process](#pull-request-process)
- [Coding Standards](#coding-standards)
- [Testing Guidelines](#testing-guidelines)
- [Documentation](#documentation)
- [Issue Reporting](#issue-reporting)

## Code of Conduct

### Our Pledge

We pledge to make participation in our project a harassment-free experience for everyone, regardless of age, body size, disability, ethnicity, gender identity and expression, level of experience, nationality, personal appearance, race, religion, or sexual identity and orientation.

### Our Standards

Examples of behavior that contributes to creating a positive environment include:
- Using welcoming and inclusive language
- Being respectful of differing viewpoints and experiences
- Gracefully accepting constructive criticism
- Focusing on what is best for the community
- Showing empathy towards other community members

## Getting Started

1. **Fork the Repository**
```bash
# Clone your fork
git clone https://github.com/your-username/smartonline.git
cd smartonline

# Add upstream remote
git remote add upstream https://github.com/original/smartonline.git
```

2. **Install Dependencies**
```bash
npm install
```

3. **Set Up Development Environment**
```bash
# Copy environment template
cp .env.example .env.local

# Generate Prisma client
npx prisma generate

# Run development server
npm run dev
```

## Development Process

### 1. Branch Naming Convention

- Feature branches: `feature/description`
- Bug fixes: `fix/description`
- Documentation: `docs/description`
- Performance improvements: `perf/description`

Example:
```bash
git checkout -b feature/add-payment-gateway
```

### 2. Commit Message Format

Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
type(scope): description

[optional body]

[optional footer]
```

Types:
- feat: New feature
- fix: Bug fix
- docs: Documentation changes
- style: Code style changes (formatting, etc.)
- refactor: Code refactoring
- perf: Performance improvements
- test: Adding or modifying tests
- chore: Maintenance tasks

Example:
```bash
git commit -m "feat(payment): add Flutterwave integration

Implements Flutterwave payment gateway integration with webhook support.

Closes #123"
```

## Pull Request Process

1. **Update Documentation**
   - Update README.md if needed
   - Add/update API documentation
   - Update technical documentation

2. **Run Tests**
```bash
# Run all tests
npm test

# Run specific test suite
npm test -- payment.test.ts
```

3. **Code Quality Checks**
```bash
# Run linter
npm run lint

# Run type checking
npm run type-check
```

4. **Create Pull Request**
   - Use the PR template
   - Reference related issues
   - Provide clear description
   - Add screenshots if UI changes

## Coding Standards

### TypeScript Guidelines

```typescript
// Use interfaces for object types
interface User {
  id: string;
  name: string;
  email: string;
}

// Use enums for fixed values
enum Role {
  ADMIN = 'ADMIN',
  INSTRUCTOR = 'INSTRUCTOR',
  STUDENT = 'STUDENT'
}

// Use type for union types
type PaymentStatus = 'pending' | 'completed' | 'failed';

// Use async/await for promises
async function getUser(id: string): Promise<User> {
  try {
    return await prisma.user.findUnique({ where: { id } });
  } catch (error) {
    throw new Error(`Failed to fetch user: ${error.message}`);
  }
}
```

### React Components

```typescript
// Use functional components
import { FC } from 'react';

interface Props {
  title: string;
  onAction: () => void;
}

const MyComponent: FC<Props> = ({ title, onAction }) => {
  return (
    <div>
      <h1>{title}</h1>
      <button onClick={onAction}>Click me</button>
    </div>
  );
};

export default MyComponent;
```

### API Routes

```typescript
// Use Next.js API routes with proper typing
import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    // Implementation
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
}
```

## Testing Guidelines

### Unit Tests

```typescript
describe('PaymentService', () => {
  it('should process payment successfully', async () => {
    const payment = await processPayment({
      amount: 100,
      currency: 'USD',
      method: 'card'
    });

    expect(payment.status).toBe('completed');
  });

  it('should handle payment failure', async () => {
    await expect(
      processPayment({
        amount: -100,
        currency: 'USD',
        method: 'card'
      })
    ).rejects.toThrow('Invalid amount');
  });
});
```

### Integration Tests

```typescript
describe('Payment API', () => {
  it('should create payment and subscription', async () => {
    const response = await request(app)
      .post('/api/payment')
      .send({
        amount: 100,
        currency: 'USD',
        planId: 'plan_123'
      });

    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty('subscriptionId');
  });
});
```

## Documentation

### Code Comments

```typescript
/**
 * Process payment through selected gateway
 * @param {PaymentDetails} details - Payment information
 * @returns {Promise<PaymentResult>} Payment processing result
 * @throws {PaymentError} When payment processing fails
 */
async function processPayment(details: PaymentDetails): Promise<PaymentResult> {
  // Implementation
}
```

### API Documentation

```typescript
/**
 * @api {post} /api/payment Process payment
 * @apiName ProcessPayment
 * @apiGroup Payment
 *
 * @apiParam {Number} amount Payment amount
 * @apiParam {String} currency Currency code
 * @apiParam {String} method Payment method
 *
 * @apiSuccess {String} id Payment ID
 * @apiSuccess {String} status Payment status
 */
```

## Issue Reporting

### Bug Reports

When reporting bugs, include:
1. Description of the bug
2. Steps to reproduce
3. Expected behavior
4. Actual behavior
5. Screenshots if applicable
6. Environment details:
   - Browser version
   - Node.js version
   - npm/yarn version
   - Operating system

### Feature Requests

When requesting features:
1. Describe the feature
2. Explain the use case
3. Suggest implementation approach
4. Provide examples of similar features
5. Include mockups if UI-related

## Review Process

### Code Review Checklist

- [ ] Code follows style guide
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No unnecessary dependencies added
- [ ] Error handling is implemented
- [ ] Performance impact is considered
- [ ] Security implications are reviewed

### Security Review

- [ ] Input validation
- [ ] Authentication checks
- [ ] Authorization rules
- [ ] Data sanitization
- [ ] SQL injection prevention
- [ ] XSS protection
- [ ] CSRF protection

## License

By contributing to SmartOnline, you agree that your contributions will be licensed under the project's MIT license.
