import Link from "next/link"
import { <PERSON><PERSON><PERSON>t, Building2, Book<PERSON>pen, Plus, Edit, Trash2, Users, Calendar, MapPin, Mail, Phone } from "lucide-react"
import { verifySession } from "@/app/lib/session"
import { redirect, notFound } from "next/navigation"
import prisma from "@/app/lib/prisma"

export default async function DepartmentDetailsPage({ 
  params 
}: { 
  params: { univId: string, departmentId: string } 
}) {
  // Verify session
  const user = await verifySession();
  if (!user) {
    redirect('/admin/login');
  }

  const { univId, departmentId } = params;

  // Verify the university belongs to this admin
  const university = await prisma.univ.findFirst({
    where: {
      id: univId,
      adminId: user.id
    },
    select: {
      id: true,
      name: true
    }
  });

  if (!university) {
    redirect('/admin/all-university');
  }

  // Fetch department details
  const department = await prisma.department.findFirst({
    where: {
      id: departmentId,
      univId: univId,
      isActive: true
    },
    include: {
      courses: {
        where: { isActive: true },
        orderBy: { createdAt: 'desc' }
      },
      instructorTokens: {
        select: {
          id: true,
          code: true,
          status: true,
          course: {
            select: { name: true, code: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      }
    }
  });

  if (!department) {
    notFound();
  }

  return (
    <div className="min-h-screen w-full bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Link 
                href={`/admin/${univId}/departments`}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Departments
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-lg font-semibold text-gray-900">{department.name}</h1>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            
            {/* Department Overview */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Department Overview</h2>
                <Link
                  href={`/admin/${univId}/departments/${departmentId}/edit`}
                  className="flex items-center gap-2 text-blue-600 hover:text-blue-700 text-sm font-medium"
                >
                  <Edit className="h-4 w-4" />
                  Edit Department
                </Link>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Department Code</label>
                  <p className="text-sm text-gray-900 font-mono">{department.code}</p>
                </div>
                
                {department.description && (
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-600 mb-1">Description</label>
                    <p className="text-sm text-gray-900">{department.description}</p>
                  </div>
                )}
                
                {department.establishedYear && (
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Established</label>
                    <p className="text-sm text-gray-900">{department.establishedYear}</p>
                  </div>
                )}
                
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Total Courses</label>
                  <p className="text-sm text-gray-900 font-medium">{department.courses.length}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Active Tokens</label>
                  <p className="text-sm text-gray-900 font-medium">
                    {department.instructorTokens.filter(token => token.status === 'active').length}
                  </p>
                </div>
              </div>
            </div>

            {/* Courses Section */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Courses</h2>
                <Link
                  href={`/admin/${univId}/departments/${departmentId}/courses/create`}
                  className="flex items-center gap-2 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-sm font-medium"
                >
                  <Plus className="h-4 w-4" />
                  Add Course
                </Link>
              </div>

              {department.courses.length > 0 ? (
                <div className="space-y-4">
                  {department.courses.map((course) => (
                    <CourseCard key={course.id} course={course} univId={univId} departmentId={departmentId} />
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No courses yet</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Add your first course to start organizing curriculum and creating instructor tokens.
                  </p>
                  <Link
                    href={`/admin/${univId}/departments/${departmentId}/courses/create`}
                    className="inline-flex items-center gap-2 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-sm font-medium"
                  >
                    <Plus className="h-4 w-4" />
                    Add First Course
                  </Link>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            
            {/* Contact Information */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
              
              <div className="space-y-4">
                {department.headOfDepartment && (
                  <div className="flex items-center gap-3">
                    <Users className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">{department.headOfDepartment}</p>
                      <p className="text-xs text-gray-500">Head of Department</p>
                    </div>
                  </div>
                )}
                
                {department.email && (
                  <div className="flex items-center gap-3">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <p className="text-sm text-gray-900">{department.email}</p>
                  </div>
                )}
                
                {department.phone && (
                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <p className="text-sm text-gray-900">{department.phone}</p>
                  </div>
                )}
                
                {department.location && (
                  <div className="flex items-center gap-3">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <p className="text-sm text-gray-900">{department.location}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              
              <div className="space-y-3">
                <Link
                  href={`/admin/${univId}/tokens?department=${departmentId}`}
                  className="w-full flex items-center justify-center gap-2 bg-green-50 text-green-700 py-2 px-3 rounded-md hover:bg-green-100 transition-colors text-sm font-medium"
                >
                  <Users className="h-4 w-4" />
                  View All Tokens
                </Link>
                
                <Link
                  href={`/admin/${univId}/departments/${departmentId}/courses/create`}
                  className="w-full flex items-center justify-center gap-2 bg-blue-50 text-blue-700 py-2 px-3 rounded-md hover:bg-blue-100 transition-colors text-sm font-medium"
                >
                  <BookOpen className="h-4 w-4" />
                  Add Course
                </Link>
                
                <Link
                  href={`/admin/${univId}/departments/${departmentId}/edit`}
                  className="w-full flex items-center justify-center gap-2 bg-gray-50 text-gray-700 py-2 px-3 rounded-md hover:bg-gray-100 transition-colors text-sm font-medium"
                >
                  <Edit className="h-4 w-4" />
                  Edit Department
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

function CourseCard({ course, univId, departmentId }: { course: any, univId: string, departmentId: string }) {
  return (
    <div className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors">
      <div className="flex items-center justify-between">
        <div>
          <h4 className="text-lg font-medium text-gray-900">{course.name}</h4>
          <p className="text-sm text-gray-500">{course.code}</p>
          {course.description && (
            <p className="text-sm text-gray-600 mt-1">{course.description}</p>
          )}
          <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
            {course.credits && <span>{course.credits} credits</span>}
            {course.semester && <span>{course.semester} {course.year}</span>}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Link
            href={`/admin/${univId}/tokens/create?course=${course.id}`}
            className="text-blue-600 hover:text-blue-700 text-sm font-medium"
          >
            Create Token
          </Link>
          <Link
            href={`/admin/${univId}/departments/${departmentId}/courses/${course.id}/edit`}
            className="p-1 text-gray-400 hover:text-gray-600"
          >
            <Edit className="h-4 w-4" />
          </Link>
        </div>
      </div>
    </div>
  )
}
