"use client"
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Shield } from 'lucide-react'
import React, { useState } from 'react'

type Props = {

}

const page = (props: Props) => {
    const [token,setToken] = useState('')
    const [isValidating,setIsValidating] = useState(false)
    const handleTokenSubmit = async () => {

    }
  return (
            <div className="flex justify-center items-center">
                <div className=" w-full max-w-[600px] p-6 bg-white rounded-lg  ">
                    <div
              className={`w-full transition-transform duration-500 ease-in-out `}
            >
              <div className="space-y-8">
                {/* Logo and Header */}
                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-6">
                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-600 text-white">
                      <Shield className="h-6 w-6" />
                    </div>
                    <span className="text-2xl font-bold text-green-600">ExamPro Admin</span>
                  </div>
                  <h2 className="text-3xl font-bold text-gray-900">Admin Registration</h2>
                  <p className="mt-2 text-sm text-gray-600">Enter your admin creation token to get started</p>
                </div>

                {/* Token Form */}
                <form onSubmit={handleTokenSubmit} className="mt-8 space-y-6">
                  <div>
                    <label htmlFor="token" className="block text-sm font-medium text-gray-700 mb-1">
                      Admin Creation Token
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Key className="h-4 w-4 text-gray-400" />
                      </div>
                      <input
                        id="token"
                        name="token"
                        type="text"
                        required
                        value={token}
                        onChange={(e) => setToken(e.target.value)}
                        className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                        placeholder="Enter your admin creation token"
                      />
                    </div>
                    <p className="mt-2 text-xs text-gray-500">
                      This token is provided by the system owner or super admin
                    </p>
                  </div>

                  <div>
                    <button
                      type="submit"
                      disabled={isValidating || token.length < 6}
                      className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isValidating ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Validating...
                        </>
                      ) : (
                        <>
                          Validate Token
                          <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                        </>
                      )}
                    </button>
                  </div>

                  {/* Help Section */}
                  <div className="bg-green-50 rounded-md p-4">
                    <h3 className="text-sm font-medium text-green-800 mb-2">Need an admin token?</h3>
                    <p className="text-xs text-green-700 mb-3">
                      Contact the system owner or super administrator to obtain an admin creation token.
                    </p>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-xs text-green-700">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                        <span>Tokens are unique per administrator</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-green-700">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                        <span>Tokens expire after 24 hours</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-green-700">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                        <span>One-time use only</span>
                      </div>
                    </div>
                  </div>

                  {/* Back to Login */}
                  <div className="text-center">
                    <p className="text-sm text-gray-600 mb-2">
                      I don't have a token?{" "}
                      <Link href="/payment" className="font-medium text-green-600 hover:text-green-500">
                        Get a token
                      </Link>
                    </p>
                    <p className="text-sm text-gray-600">
                      Already have an account?{" "}
                      <Link href="/admin/login" className="font-medium text-green-600 hover:text-green-500">
                        Sign in
                      </Link>
                    </p>

                  </div>
                </form>
              </div>
            </div>
                </div>
            </div>

  )
}

export default page