import 'server-only'
import { SignJWT, jwtVerify } from 'jose'
import { <PERSON><PERSON> } from 'next/font/google'
import { cookies } from 'next/headers'

type tokenPayload = {
    email: string,
    subscriptionId: string,
    adminId:string
}
const key = new TextEncoder() . encode (process.env.UNIV_SECRET)

const cookie = {
    name: 'university-creation-token',
    options: {httpOnly: true, secure: true, sameSite: 'lax', path: '/'},
    duration: 90 * 24 * 60 * 60 * 1000,
} 
export async function encrypt(payload:tokenPayload) {
    return new SignJWT(payload)
    .setProtectedHeader ({ alg: 'HS256' })
    .setIssuedAt ()
    .setExpirationTime ('90d')
    .sign (key)
}
export async function decrypt(token:any) {
    try {
        const { payload } = await jwtVerify(token, key, {
            algorithms: ['HS256']
          })
return payload as tokenPayload
    } catch (error) {
        return null
    }
}

export async function createToken(payload:tokenPayload) {
const expires = new Date(Date.now() + cookie.duration)
const token = await encrypt (payload) 

const Cookies = await cookies();
    Cookies.set(cookie.name,token,{...cookie.options,sameSite:cookie.options.sameSite as 'lax',expires:expires})
    return token
}

export async function verifyToken() {
    const Cookies = await cookies()
    const enc_cookie = Cookies.get(cookie.name)?.value
    const token = await decrypt(enc_cookie)
    if (!token) {
        return null
    }
return token
}

export async function deleteToken() {
 (await cookies()).delete(cookie.name)
}

export async function getToken() {
    const Cookies = await cookies()
    const cookie = Cookies.get(cookies.name)?.value
    return cookie
}