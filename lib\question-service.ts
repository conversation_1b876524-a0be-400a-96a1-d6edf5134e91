import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface CreateQuestionRequest {
  title: string;
  text: string;
  type: 'multiple-choice' | 'essay' | 'true-false' | 'short-answer';
  subject: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  tags: string[];
  options?: string[]; // For multiple choice
  correctAnswer?: number; // For multiple choice
  wordLimit?: number; // For essay
  courseId?: string;
  departmentId?: string;
  univId: string;
  createdBy: string;
}

export interface QuestionFilter {
  subject?: string;
  difficulty?: string;
  type?: string;
  tags?: string[];
  courseId?: string;
  departmentId?: string;
  univId: string;
  search?: string;
}

export class QuestionService {
  /**
   * Create a new question
   */
  static async createQuestion(data: CreateQuestionRequest): Promise<string> {
    try {
      const question = await prisma.question.create({
        data: {
          title: data.title,
          text: data.text,
          type: data.type,
          subject: data.subject,
          difficulty: data.difficulty,
          tags: data.tags,
          options: data.options || [],
          correctAnswer: data.correctAnswer,
          wordLimit: data.wordLimit,
          courseId: data.courseId,
          departmentId: data.departmentId,
          univId: data.univId,
          createdBy: data.createdBy,
        },
      });

      console.log('Question created successfully:', question.id);
      return question.id;
    } catch (error: any) {
      console.error('Question creation failed:', error);
      throw new Error(error.message || 'Failed to create question');
    }
  }

  /**
   * Get questions with filtering and pagination
   */
  static async getQuestions(
    filter: QuestionFilter,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    questions: any[];
    total: number;
    totalPages: number;
    currentPage: number;
  }> {
    try {
      const skip = (page - 1) * limit;

      // Build where clause
      const where: any = {
        univId: filter.univId,
        isActive: true,
      };

      if (filter.subject) {
        where.subject = filter.subject;
      }

      if (filter.difficulty) {
        where.difficulty = filter.difficulty;
      }

      if (filter.type) {
        where.type = filter.type;
      }

      if (filter.courseId) {
        where.courseId = filter.courseId;
      }

      if (filter.departmentId) {
        where.departmentId = filter.departmentId;
      }

      if (filter.tags && filter.tags.length > 0) {
        where.tags = {
          hasSome: filter.tags,
        };
      }

      if (filter.search) {
        where.OR = [
          { title: { contains: filter.search, mode: 'insensitive' } },
          { text: { contains: filter.search, mode: 'insensitive' } },
          { subject: { contains: filter.search, mode: 'insensitive' } },
        ];
      }

      // Get total count
      const total = await prisma.question.count({ where });

      // Get questions
      const questions = await prisma.question.findMany({
        where,
        include: {
          course: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
          department: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      });

      const totalPages = Math.ceil(total / limit);

      return {
        questions: questions.map((q) => ({
          id: q.id,
          title: q.title,
          text: q.text,
          type: q.type,
          subject: q.subject,
          difficulty: q.difficulty,
          tags: q.tags,
          options: q.options,
          correctAnswer: q.correctAnswer,
          wordLimit: q.wordLimit,
          course: q.course,
          department: q.department,
          lastEdited: q.updatedAt.toISOString().split('T')[0],
          createdAt: q.createdAt,
          updatedAt: q.updatedAt,
        })),
        total,
        totalPages,
        currentPage: page,
      };
    } catch (error) {
      console.error('Failed to get questions:', error);
      throw new Error('Failed to retrieve questions');
    }
  }

  /**
   * Get a single question by ID
   */
  static async getQuestionById(id: string, univId: string): Promise<any | null> {
    try {
      const question = await prisma.question.findFirst({
        where: {
          id,
          univId,
          isActive: true,
        },
        include: {
          course: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
          department: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
        },
      });

      if (!question) {
        return null;
      }

      return {
        id: question.id,
        title: question.title,
        text: question.text,
        type: question.type,
        subject: question.subject,
        difficulty: question.difficulty,
        tags: question.tags,
        options: question.options,
        correctAnswer: question.correctAnswer,
        wordLimit: question.wordLimit,
        course: question.course,
        department: question.department,
        lastEdited: question.updatedAt.toISOString().split('T')[0],
        createdAt: question.createdAt,
        updatedAt: question.updatedAt,
      };
    } catch (error) {
      console.error('Failed to get question:', error);
      return null;
    }
  }

  /**
   * Update a question
   */
  static async updateQuestion(
    id: string,
    univId: string,
    data: Partial<CreateQuestionRequest>
  ): Promise<boolean> {
    try {
      await prisma.question.updateMany({
        where: {
          id,
          univId,
          isActive: true,
        },
        data: {
          ...data,
          updatedAt: new Date(),
        },
      });

      return true;
    } catch (error) {
      console.error('Failed to update question:', error);
      return false;
    }
  }

  /**
   * Delete a question (soft delete)
   */
  static async deleteQuestion(id: string, univId: string): Promise<boolean> {
    try {
      await prisma.question.updateMany({
        where: {
          id,
          univId,
          isActive: true,
        },
        data: {
          isActive: false,
          updatedAt: new Date(),
        },
      });

      return true;
    } catch (error) {
      console.error('Failed to delete question:', error);
      return false;
    }
  }

  /**
   * Get question statistics for a university
   */
  static async getQuestionStats(univId: string): Promise<{
    total: number;
    bySubject: Record<string, number>;
    byDifficulty: Record<string, number>;
    byType: Record<string, number>;
  }> {
    try {
      const questions = await prisma.question.findMany({
        where: {
          univId,
          isActive: true,
        },
        select: {
          subject: true,
          difficulty: true,
          type: true,
        },
      });

      const total = questions.length;
      const bySubject: Record<string, number> = {};
      const byDifficulty: Record<string, number> = {};
      const byType: Record<string, number> = {};

      questions.forEach((q) => {
        bySubject[q.subject] = (bySubject[q.subject] || 0) + 1;
        byDifficulty[q.difficulty] = (byDifficulty[q.difficulty] || 0) + 1;
        byType[q.type] = (byType[q.type] || 0) + 1;
      });

      return {
        total,
        bySubject,
        byDifficulty,
        byType,
      };
    } catch (error) {
      console.error('Failed to get question stats:', error);
      return {
        total: 0,
        bySubject: {},
        byDifficulty: {},
        byType: {},
      };
    }
  }
}
