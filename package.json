{"name": "landhaven", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@heroicons/react": "^2.2.0", "@prisma/client": "^6.8.2", "@prisma/extension-accelerate": "^2.0.1", "@radix-ui/react-icons": "^1.3.0", "@sanity/image-url": "^1.1.0", "@sanity/vision": "^3.64.2", "@svgr/webpack": "^8.1.0", "@types/bcrypt": "^5.0.2", "axios": "^1.9.0", "bcrypt": "^5.1.1", "chart.js": "^4.4.9", "chartjs": "^0.3.24", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookie": "^1.0.2", "jose": "^5.9.6", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.453.0", "multer": "^2.0.1", "next": "15.0.1", "next-sanity": "^9.8.16", "next-themes": "^0.3.0", "nodemailer": "^7.0.3", "prisma": "^6.8.2", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-webcam": "^7.2.0", "sanity": "^3.64.2", "sonner": "^1.5.0", "styled-components": "^6.1.13", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.3"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.0.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "tailwindcss": "^3.4.1", "ts-jest": "^29.1.2", "tsx": "^4.19.4", "typescript": "^5"}}