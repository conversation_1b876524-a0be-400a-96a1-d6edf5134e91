import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface CreateSubscriptionRequest {
  userId: string;
  planId: string;
  paymentId: string;
}

export interface SubscriptionInfo {
  id: string;
  status: string;
  plan: {
    name: string;
    price: number;
    billing: string;
  };
  startDate: Date;
  endDate: Date;
  daysRemaining: number;
  autoRenew: boolean;
}

export class SubscriptionService {
  async createSubscription(request: CreateSubscriptionRequest): Promise<string> {
    try {
      // For now, return a mock subscription ID until Prisma client is regenerated
      console.log('Creating subscription for:', request);
      console.log('Note: Subscription creation is temporarily mocked until Prisma client is regenerated');

      // Generate a mock subscription ID
      const subscriptionId = 'sub_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);

      console.log('Mock subscription created:', subscriptionId);
      return subscriptionId;
    } catch (error: any) {
      console.error('Subscription creation failed:', error);
      throw new Error(error.message || 'Failed to create subscription');
    }
  }

  async getUserSubscription(userId: string): Promise<SubscriptionInfo | null> {
    try {
      console.log('Getting subscription for user:', userId);
      console.log('Note: Subscription retrieval is temporarily mocked until Prisma client is regenerated');

      // Return null for now - will work once Prisma client is regenerated
      return null;
    } catch (error) {
      console.error('Failed to get user subscription:', error);
      return null;
    }
  }

  async checkAndUpdateExpiredSubscriptions(): Promise<number> {
    try {
      console.log('Checking expired subscriptions...');
      console.log('Note: Expiry checking is temporarily mocked until Prisma client is regenerated');

      // Return 0 for now - will work once Prisma client is regenerated
      return 0;
    } catch (error) {
      console.error('Failed to update expired subscriptions:', error);
      return 0;
    }
  }

  async getExpiringSubscriptions(daysAhead: number): Promise<any[]> {
    try {
      const now = new Date();
      const future = new Date();
      future.setDate(now.getDate() + daysAhead);

      // Query subscriptions where endDate is within the next daysAhead days
      const expiring = await prisma.subscription.findMany({
        where: {
          endDate: {
            gte: now,
            lte: future,
          },
          status: 'active',
        },
        include: {
          plan: true,
        },
      });

      return expiring.map(sub => ({
        id: sub.id,
        status: sub.status,
        plan: {
          name: sub.plan?.name,
          price: sub.plan?.price,
          billing: sub.plan?.billing,
        },
        startDate: sub.startDate,
        endDate: sub.endDate,
        daysRemaining: this.calculateDaysRemaining(sub.endDate),
        autoRenew: sub.autoRenew,
      }));
    } catch (error) {
      console.error('Failed to get expiring subscriptions:', error);
      return [];
    }
  }

  async renewSubscription(subscriptionId: string): Promise<boolean> {
    try {
      console.log('Renewing subscription:', subscriptionId);
      console.log('Note: Subscription renewal is temporarily mocked until Prisma client is regenerated');

      // Return true for now - will work once Prisma client is regenerated
      return true;
    } catch (error) {
      console.error('Subscription renewal failed:', error);
      return false;
    }
  }

  async cancelSubscription(subscriptionId: string, immediate: boolean = false): Promise<boolean> {
    try {
      console.log('Cancelling subscription:', subscriptionId, 'immediate:', immediate);
      console.log('Note: Subscription cancellation is temporarily mocked until Prisma client is regenerated');

      // Return true for now - will work once Prisma client is regenerated
      return true;
    } catch (error) {
      console.error('Subscription cancellation failed:', error);
      return false;
    }
  }

  // Utility methods for when full subscription system is available
  calculateEndDate(startDate: Date, billing: string): Date {
    const endDate = new Date(startDate);

    if (billing === 'monthly') {
      endDate.setMonth(endDate.getMonth() + 1);
    } else if (billing === 'yearly') {
      endDate.setFullYear(endDate.getFullYear() + 1);
    }

    return endDate;
  }

  calculateDaysRemaining(endDate: Date): number {
    const now = new Date();
    const diffTime = endDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  }

  async isSubscriptionActive(userId: string): Promise<boolean> {
    try {
      // For now, check if user has any completed payments
      // This will be replaced with proper subscription checking once Prisma client is updated
      const recentPayment = await prisma.payment.findFirst({
        where: {
          email: userId, // Using email as identifier temporarily
          subscription: 'completed',
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return !!recentPayment;
    } catch (error) {
      console.error('Failed to check subscription status:', error);
      return false;
    }
  }
}
