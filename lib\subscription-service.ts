import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface CreateSubscriptionRequest {
  userId: string;
  planId: string;
  paymentId: string;
}

export interface SubscriptionInfo {
  id: string;
  status: string;
  plan: {
    name: string;
    price: number;
    billing: string;
  };
  startDate: Date;
  endDate: Date;
  daysRemaining: number;
  autoRenew: boolean;
}

export class SubscriptionService {
  async createSubscription(request: CreateSubscriptionRequest): Promise<string> {
    try {
      console.log('Creating subscription for:', request);

      // Get the plan details to calculate end date
      const plan = await prisma.plan.findUnique({
        where: { id: request.planId },
      });

      if (!plan) {
        throw new Error(`Plan with ID ${request.planId} not found`);
      }

      // Calculate subscription dates
      const startDate = new Date();
      const endDate = this.calculateEndDate(startDate, plan.billing);

      // Create the subscription
      const subscription = await prisma.subscription.create({
        data: {
          adminId: request.userId,
          planId: request.planId,
          paymentId: request.paymentId,
          status: 'active',
          activeDate: startDate.toISOString(),
        },
      });

      console.log('Subscription created successfully:', subscription.id);
      return subscription.id;
    } catch (error: any) {
      console.error('Subscription creation failed:', error);
      throw new Error(error.message || 'Failed to create subscription');
    }
  }

  async getUserSubscription(userId: string): Promise<SubscriptionInfo | null> {
    try {
      console.log('Getting subscription for user:', userId);

      // Get the most recent active subscription for the user
      const subscription = await prisma.subscription.findFirst({
        where: {
          adminId: userId,
          status: 'active',
        },
        include: {
          plan: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      if (!subscription || !subscription.plan) {
        return null;
      }

      // Calculate end date and days remaining
      const startDate = new Date(subscription.activeDate);
      const endDate = this.calculateEndDate(startDate, subscription.plan.billing);
      const daysRemaining = this.calculateDaysRemaining(endDate);

      return {
        id: subscription.id,
        status: subscription.status,
        plan: {
          name: subscription.plan.name,
          price: subscription.plan.price,
          billing: subscription.plan.billing,
        },
        startDate,
        endDate,
        daysRemaining,
        autoRenew: false, // TODO: Add autoRenew field to schema if needed
      };
    } catch (error) {
      console.error('Failed to get user subscription:', error);
      return null;
    }
  }

  async checkAndUpdateExpiredSubscriptions(): Promise<number> {
    try {
      console.log('Checking expired subscriptions...');
      console.log('Note: Expiry checking is temporarily mocked until Prisma client is regenerated');

      // Return 0 for now - will work once Prisma client is regenerated
      return 0;
    } catch (error) {
      console.error('Failed to update expired subscriptions:', error);
      return 0;
    }
  }

  async getExpiringSubscriptions(daysAhead: number): Promise<any[]> {
    try {
      const now = new Date();
      const future = new Date();
      future.setDate(now.getDate() + daysAhead);

      // Query subscriptions where endDate is within the next daysAhead days
      const expiring = await prisma.subscription.findMany({
        where: {
          endDate: {
            gte: now,
            lte: future,
          },
          status: 'active',
        },
        include: {
          plan: true,
        },
      });

      return expiring.map((sub: any) => ({
        id: sub.id,
        status: sub.status,
        plan: {
          name: sub.plan?.name,
          price: sub.plan?.price,
          billing: sub.plan?.billing,
        },
        startDate: sub.startDate,
        endDate: sub.endDate,
        daysRemaining: this.calculateDaysRemaining(sub.endDate),
        autoRenew: sub.autoRenew,
      }));
    } catch (error) {
      console.error('Failed to get expiring subscriptions:', error);
      return [];
    }
  }

  async renewSubscription(subscriptionId: string): Promise<boolean> {
    try {
      console.log('Renewing subscription:', subscriptionId);
      console.log('Note: Subscription renewal is temporarily mocked until Prisma client is regenerated');

      // Return true for now - will work once Prisma client is regenerated
      return true;
    } catch (error) {
      console.error('Subscription renewal failed:', error);
      return false;
    }
  }

  async cancelSubscription(subscriptionId: string, immediate: boolean = false): Promise<boolean> {
    try {
      console.log('Cancelling subscription:', subscriptionId, 'immediate:', immediate);
      console.log('Note: Subscription cancellation is temporarily mocked until Prisma client is regenerated');

      // Return true for now - will work once Prisma client is regenerated
      return true;
    } catch (error) {
      console.error('Subscription cancellation failed:', error);
      return false;
    }
  }

  // Utility methods for when full subscription system is available
  calculateEndDate(startDate: Date, billing: string): Date {
    const endDate = new Date(startDate);

    if (billing === 'monthly') {
      endDate.setMonth(endDate.getMonth() + 1);
    } else if (billing === 'yearly') {
      endDate.setFullYear(endDate.getFullYear() + 1);
    }

    return endDate;
  }

  calculateDaysRemaining(endDate: Date): number {
    const now = new Date();
    const diffTime = endDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  }

  async isSubscriptionActive(userId: string): Promise<boolean> {
    try {
      // For now, check if user has any completed payments
      // This will be replaced with proper subscription checking once Prisma client is updated
      const recentPayment = await prisma.payment.findFirst({
        where: {
          email: userId, // Using email as identifier temporarily
          subscription: 'completed',
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return !!recentPayment;
    } catch (error) {
      console.error('Failed to check subscription status:', error);
      return false;
    }
  }
}
