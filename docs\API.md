# SmartOnline API Documentation

## API Overview

The SmartOnline API is organized around REST principles. All requests should be made over HTTPS in production, and all data is sent and received as JSON.

## Base URL

```
Production: https://api.smartonline.com
Development: http://localhost:3000/api
```

## Authentication

### JWT Authentication
All API requests must include a valid JWT token in the Authorization header:

```http
Authorization: Bearer <your_jwt_token>
```

## API Endpoints

### University Management

#### Create University
```http
POST /api/admin/create-university
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "Example University",
  "domain": "example.edu",
  "location": "City, Country",
  "contactEmail": "<EMAIL>",
  "logo": "base64_encoded_image"
}

Response: 200 OK
{
  "id": "univ_123",
  "name": "Example University",
  "domain": "example.edu",
  "status": "pending"
}
```

#### Get University Dashboard
```http
GET /api/admin/{univId}/dashboard
Authorization: Bearer <token>

Response: 200 OK
{
  "statistics": {
    "totalStudents": 1500,
    "activeInstructors": 75,
    "departments": 12,
    "activeCourses": 150
  },
  "recentActivity": [...]
}
```

### Department Management

#### Create Department
```http
POST /api/admin/{univId}/departments
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "Computer Science",
  "code": "CS",
  "description": "Department of Computer Science",
  "headOfDepartment": "Dr. John Doe"
}

Response: 200 OK
{
  "id": "dept_123",
  "name": "Computer Science",
  "code": "CS"
}
```

### Student Management

#### Create Student Account
```http
POST /api/auth/student/create
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "securepassword",
  "universityId": "univ_123",
  "departmentId": "dept_123"
}

Response: 200 OK
{
  "id": "student_123",
  "name": "John Doe",
  "email": "<EMAIL>"
}
```

#### Student Login
```http
POST /api/auth/student/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword"
}

Response: 200 OK
{
  "token": "jwt_token_here",
  "user": {
    "id": "student_123",
    "name": "John Doe",
    "email": "<EMAIL>"
  }
}
```

### Payment Processing

#### Initialize Payment
```http
POST /api/payment
Content-Type: application/json
Authorization: Bearer <token>

{
  "amount": 100.00,
  "currency": "XAF",
  "method": "flutterwave",
  "planId": "plan_123"
}

Response: 200 OK
{
  "paymentId": "pay_123",
  "checkoutUrl": "https://payment-gateway.com/checkout",
  "transactionId": "trans_123"
}
```

#### Verify Payment
```http
POST /api/payment/verify
Content-Type: application/json
Authorization: Bearer <token>

{
  "transactionId": "trans_123"
}

Response: 200 OK
{
  "status": "success",
  "subscription": {
    "id": "sub_123",
    "status": "active",
    "expiryDate": "2024-12-31"
  }
}
```

### Subscription Management

#### Get Subscription Status
```http
GET /api/subscriptions/{id}
Authorization: Bearer <token>

Response: 200 OK
{
  "id": "sub_123",
  "status": "active",
  "plan": {
    "name": "Premium",
    "features": [...]
  },
  "expiryDate": "2024-12-31"
}
```

#### Update Subscription
```http
PUT /api/subscriptions/{id}
Content-Type: application/json
Authorization: Bearer <token>

{
  "status": "cancelled",
  "reason": "Upgrade to different plan"
}

Response: 200 OK
{
  "id": "sub_123",
  "status": "cancelled",
  "cancelledAt": "2024-03-15T12:00:00Z"
}
```

### Token Management

#### Generate Instructor Tokens
```http
POST /api/admin/{univId}/tokens
Content-Type: application/json
Authorization: Bearer <token>

{
  "departmentId": "dept_123",
  "courseId": "course_123",
  "quantity": 50,
  "expirationDays": 30
}

Response: 200 OK
{
  "tokens": [
    {
      "code": "INS-123-ABC",
      "expirationDate": "2024-04-15T00:00:00Z"
    },
    ...
  ]
}
```

## Webhook Events

### Payment Webhooks

#### Flutterwave Webhook
```http
POST /api/webhooks/flutterwave
Content-Type: application/json

{
  "event": "charge.completed",
  "data": {
    "id": "trans_123",
    "status": "successful",
    "amount": 100.00
  }
}
```

#### MTN Mobile Money Webhook
```http
POST /api/webhooks/mtn
Content-Type: application/json

{
  "transactionId": "mtn_123",
  "status": "SUCCESSFUL",
  "amount": 100.00
}
```

## Error Handling

### Error Response Format
```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable message",
    "details": {
      "field": "Additional error context"
    }
  }
}
```

### Common Error Codes

| Code | Description |
|------|-------------|
| 400  | Bad Request - Invalid parameters |
| 401  | Unauthorized - Invalid or missing token |
| 403  | Forbidden - Insufficient permissions |
| 404  | Not Found - Resource doesn't exist |
| 422  | Unprocessable Entity - Validation failed |
| 429  | Too Many Requests - Rate limit exceeded |
| 500  | Internal Server Error |

### Validation Errors
```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input parameters",
    "details": {
      "email": "Must be a valid email address",
      "password": "Must be at least 8 characters"
    }
  }
}
```

## Rate Limiting

- Anonymous IP: 100 requests per hour
- Authenticated user: 1000 requests per hour
- Webhook endpoints: 10000 requests per hour

Rate limit headers included in responses:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1616789000
```

## Pagination

### Request Parameters
```
?page=1&limit=20
```

### Response Format
```json
{
  "data": [...],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 20,
    "pages": 5
  }
}
```

## API Versioning

The API version is included in the URL:
```
/api/v1/endpoint
```

## Development Tools

### API Testing
```bash
# Using curl
curl -X POST https://api.smartonline.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Using Postman
Import the provided Postman collection: smartonline-api.postman_collection.json
```

### Sandbox Environment
```
https://sandbox.smartonline.com/api
```

Use test credentials:
- Email: <EMAIL>
- Password: test123
