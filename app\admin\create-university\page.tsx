import { verifyToken } from '@/app/lib/token'
import React from 'react'
import CreateUniversity from './Form'
import { redirect } from 'next/navigation'
import prisma from '@/app/lib/prisma'

type Props = {}

const page = async (props: Props) => {
  const creationSession = await verifyToken()
  console.log(creationSession)
  if(!creationSession){

  redirect("/admin/subscriptions")
  
}
const subs = await prisma.subscription.findUnique({where:{id:creationSession.subscriptionId,status:'unactive'}})

if(!subs){
  redirect("/admin/subscriptions")
}

  return (
    <CreateUniversity subscriptionId={creationSession.subscriptionId}/>
  )
}

export default page