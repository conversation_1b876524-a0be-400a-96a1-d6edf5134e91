'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
 
export default function LoginPage({univID}:{univID:string}) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    if (!email || !password) {
      setError('Please enter both email and password.');
      setLoading(false);
      return;
    }

    try {
      const res = await fetch('/api/auth/student/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password,univID }),
      });

      const data = await res.json();

      if (!res.ok) {
        setError(data.error || 'Login failed');
      } else {
        if(data.role == "admin") router.push(`/univ/${univID}/ad/dashboard`) 
        if(data.role == "student") router.push(`/univ/${univID}/st/dashboard`) 
      }
    } catch {
      setError('Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex">
      {/* Left side - Login form */}
      <div className="flex-1 flex items-center justify-center p-8 bg-green-50">
        <div className="w-full max-w-md space-y-8">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-green-500 rounded-sm flex items-center justify-center">
              <div className="w-3 h-3 border border-white rounded-sm"></div>
            </div>
            <span className="text-xl font-semibold text-green-500">KING'S COMPANY</span>
          </div>

          {/* Header */}
          <div className="space-y-2">
            <h1 className="text-3xl font-bold text-green-500">Welcome Back</h1>
            <p className="text-sm text-gray-700">Log in to access your exams and dashboard</p>
          </div>

          {/* Form */}
          {error && <div className="text-red-600 text-sm">{error}</div>}
          <form onSubmit={handleLogin} className="space-y-6">
            <div className="space-y-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-black mb-1">
                  Email<span className="text-red-500">*</span>
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full h-12 px-3 border border-gray-300 text-black rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  required
                />
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-black mb-1">
                  Password<span className="text-red-500">*</span>
                </label>
                <input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full h-12 px-3 border border-gray-300 text-black rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  required
                />
              </div>
            </div>

            <button
              type="submit"
              disabled={loading}
              className={`w-full h-12 bg-green-500 hover:bg-green-600 text-white font-medium rounded-md transition-colors ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Logging In…' : 'Log In'}
            </button>
          </form>

          {/* Links */}
          <p className="text-center text-sm text-black">
            Don’t have an account?{' '}
            <Link href="/univ/1/admin/sign-up" className="text-green-500 hover:underline">
              Create one
            </Link>
          </p>
          <p className="text-center text-sm text-green-500 hover:underline">
            <Link href="/univ/1/admin/forgot-password">Forgot password?</Link>
          </p>
        </div>
      </div>

      {/* Right side - Hero section */}
      <div className="flex-1 relative bg-gray-100 ">
        <div className="absolute inset-0 bg-black/40" />
        <div className="absolute top-0 translate-y-4/5 left-0 right-0 p-12 text-white">
          <h2 className="text-4xl font-bold mb-4 leading-tight space-y-20">
            Smart Exam Management <br />
            Designed for You
          </h2>
          <p className="text-lg mb-8 opacity-90 max-w-md">
            Seamless login, secure dashboard, and all your academic tools in one place.
          </p>
        </div>
      </div>
    </div>
  );
}
