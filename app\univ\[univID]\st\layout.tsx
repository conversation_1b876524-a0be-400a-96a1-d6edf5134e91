import React from 'react'
import <PERSON>Bar from './_component/SideBar'
import Header from './_component/Header'

const layout = ({children}: {children: React.ReactNode}) => {
  return (
    <div className='flex  h-screen bg-gray-50'>
        <div className="overflow-y-auto overflow-x-hidden ">
            <SideBar />
        </div>
        <div className="w-full flex-1 flex flex-col">
          <Header/>
          {children}</div>
    </div>
  )
}

export default layout