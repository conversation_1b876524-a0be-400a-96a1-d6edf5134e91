# SmartOnline Environment Variables Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL database connection string
# Format: postgresql://username:password@localhost:5432/database_name
DATABASE_URL="postgresql://user:password@localhost:5432/smartonline"

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
# JWT secret key for token signing (use a strong, random string)
JWT_SECRET="your-secure-jwt-secret-key-here"

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Base URL for the application (no trailing slash)
NEXT_PUBLIC_BASE_URL="http://localhost:3000"

# Node environment
NODE_ENV="development"

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
# SMTP server configuration for sending emails
SMTP_HOST="your-smtp-host"
SMTP_PORT="587"
SMTP_USER="your-smtp-username"
SMTP_PASS="your-smtp-password"

# Alternative email configuration (if using different naming)
EMAIL_USER="your-email-username"
EMAIL_PASS="your-email-password"

# =============================================================================
# PAYMENT GATEWAY CONFIGURATION
# =============================================================================
# Flutterwave API keys
# Get these from your Flutterwave dashboard
# Use TEST keys for development, LIVE keys for production
FLUTTERWAVE_PUBLIC_KEY="FLWPUBK_TEST-your-public-key-here"
FLUTTERWAVE_SECRET_KEY="FLWSECK_TEST-your-secret-key-here"

# PayPal configuration (for international payments)
PAYPAL_CLIENT_ID="your-paypal-client-id"
PAYPAL_CLIENT_SECRET="your-paypal-client-secret"

# =============================================================================
# SANITY CMS CONFIGURATION
# =============================================================================
# Sanity project configuration
# Get these from your Sanity dashboard
NEXT_PUBLIC_SANITY_PROJECT_ID="your-sanity-project-id"
NEXT_PUBLIC_SANITY_DATASET="production"

# Sanity API token (for write operations)
SANITY_API_TOKEN="your-sanity-api-token"

# =============================================================================
# OPTIONAL CONFIGURATIONS
# =============================================================================
# File upload configuration
MAX_FILE_SIZE="10485760" # 10MB in bytes
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,application/pdf"

# Rate limiting
RATE_LIMIT_MAX="100" # requests per window
RATE_LIMIT_WINDOW="900000" # 15 minutes in milliseconds

# Session configuration
SESSION_SECRET="your-session-secret-key"
SESSION_MAX_AGE="86400000" # 24 hours in milliseconds

# =============================================================================
# DEVELOPMENT/DEBUG CONFIGURATION
# =============================================================================
# Enable debug mode (development only)
DEBUG="false"

# Log level (error, warn, info, debug)
LOG_LEVEL="info"

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================
# Analytics (if using)
GOOGLE_ANALYTICS_ID="your-ga-id"

# Error tracking (if using Sentry)
SENTRY_DSN="your-sentry-dsn"

# =============================================================================
# MOBILE MONEY CONFIGURATION (Optional)
# =============================================================================
# MTN Mobile Money
MTN_API_KEY="your-mtn-api-key"
MTN_API_SECRET="your-mtn-api-secret"

# Orange Money
ORANGE_API_KEY="your-orange-api-key"
ORANGE_API_SECRET="your-orange-api-secret"

# =============================================================================
# NOTES
# =============================================================================
# 1. Never commit the actual .env file to version control
# 2. Use strong, unique passwords and secrets
# 3. For production, use environment-specific values
# 4. Restart your development server after changing environment variables
# 5. Some variables are required for the application to function properly
# 6. Test your configuration using the debug endpoints in development
