"use server"
import {SignJWT,jwtVerify} from 'jose'
import { cookies } from 'next/headers'
import prisma from './prisma'
const key =  new TextEncoder().encode(process.env.JWT_SECRET)

const cookie = {
    name: 'session',
    options: { httpOnly: true, secure: true, sameSite: 'lax' as 'lax', path: '/' },
    duration: 24 * 60 * 60 * 1000
}
type userCookieData = {
    id:string,
    email:string,
    role:string,
    university?:{
        id:string
    }
}
export async function encrypt(payload:{user:userCookieData,expires:Date}){
 return new SignJWT(payload)
 .setProtectedHeader({alg:'HS256'})
 .setIssuedAt()
 .setExpirationTime('24h')
 .sign(key)
}
export async function decrypt(session:string){
    try {
        const {payload}  = await jwtVerify(session,key,{
            algorithms:['HS256'],
        })
        return payload as {user:userCookieData,expires:Date}
    } catch (error) {
        return null        
    }
}



export async function createSession(user:userCookieData){
    const expires = new Date(Date.now()+cookie.duration)
    const session = await encrypt({user,expires})
    const cookieStore = await cookies();
    cookieStore.set(cookie.name, session, { ...cookie.options, expires });
}

export async function verifySession() {
    const cookieStore = await cookies();
    const session = cookieStore.get(cookie.name)?.value;
    if(!session) return null
    const decrypted = await decrypt(session)
    if (!decrypted) return null
    const {user,expires} = decrypted

    if(new Date() > new Date(expires)) return null
    const admin = await prisma.admin.findFirst({where:{id:user.id}})
    if(!admin) return null
    return user    
}

export async function deleteSession() {
    const cookieStore = await cookies();
    cookieStore.delete(cookie.name);
}