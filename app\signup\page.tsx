"use client"
import { useState } from "react"
import { toast } from "sonner";

const test2 = () => {
    const [name, setName] = useState('');
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()
        if (password !== confirmPassword) {
            toast.error("Passwords do not match");
            return;
        }
        if (name === '' || email === '' || password === '' || confirmPassword === '') {
            toast.error("All fields are required");
            return;
        }
        // Here you can add your signup logic, like calling an API to create a user
        const response = await fetch('/api/signup', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ name, email, password }),
        });
        if (response.status == 200) {
            const data = await response.json();
            toast.success(data.message);
            // Optionally redirect or clear the form
            setName('');
            setEmail('');
            setPassword('');
            setConfirmPassword('');
        }
        else {
            const errorData = await response.json();
            toast.error(errorData.message || "Signup failed");
        }
        // You can also handle errors here, like showing a toast notification
        // or redirecting the user to a different page.
        // For example:
        
    }
    return(
        <div className="w-full h-screen bg-violet-100 flex justify-center items-center">

        <form onSubmit={handleSubmit} className="w-[400px] p-1 bg-blue-300 rounded-md">
            <div>
                <label className="block mb-2 text-sm font-medium text-black dark:text-black">Your Name</label>
                <input onChange={(e) => {setName(e.target.value)}} type="text" className="bg-[#fbfbfb] px-2 py-1 w-full outline-none text-black border-[1px] border-violet-400 rounded" />
            </div>
            <div className="mt-4">
                <label className="block mb-2 text-sm font-medium text-black dark:text-black">Your Email</label>
                <input onChange={(e) => {setEmail(e.target.value)}} type="email" className="bg-[#fdfbfb] px-2 py-1 w-full outline-none text-black border-[1px] border-violet-400 rounded" />
                </div>
            <div className="mt-4">
            <label className="block mb-2 text-sm font-medium text-black dark:text-black">create password</label>
                <input onChange={(e) => {setPassword(e.target.value)}} type="password" className="bg-[#fdfbfb] px-2 py-1 w-full outline-none text-black border-[1px] border-violet-400 rounded" />
            </div>
            <div className="mt-4">
            <label className="block mb-2 text-sm font-medium text-black dark:text-black">confirm password</label>
                <input onChange={(e) => {setConfirmPassword(e.target.value)}}type="password" className="bg-[#f4efef] px-2 py-1 w-full outline-none text-black border-[1px] border-violet-400 rounded" />
                <button type="submit" className="w-full mt-4 bg-violet-500 text-white px-4 py-2 rounded hover:bg-violet-600 transition duration-200">Submit</button>
                </div>

                
        </form>
        </div>
    )
}
export default test2