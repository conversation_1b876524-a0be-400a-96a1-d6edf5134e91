"use client"

import { useState } from "react"
import {
  <PERSON><PERSON><PERSON>,
  User,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Shield,
  Key,
  HelpCircle,
  Save,
  Eye,
  EyeOff,
  Camera,
  Smartphone,
  Monitor,
  Sun,
  Moon,
  Download,
  Trash2,
  LinkIcon,
  X,
} from "lucide-react"

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState("profile")
  const [showPassword, setShowPassword] = useState(false)
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false)
  const [darkMode, setDarkMode] = useState(false)
  const [notifications, setNotifications] = useState({
    email: {
      newRegistrations: true,
      submissionsToGrade: true,
      platformAnnouncements: false,
    },
    inApp: {
      unreadMessages: true,
      examReminders: true,
      gradeUpdates: false,
    },
  })

  const tabs = [
    { id: "profile", name: "Profile", icon: User },
    { id: "account", name: "Account & Security", icon: Lock },
    { id: "notifications", name: "Notifications", icon: <PERSON> },
    { id: "appearance", name: "Appearance", icon: Palette },
    { id: "privacy", name: "Privacy & Data", icon: Shield },
    { id: "integrations", name: "Integrations", icon: Key },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="flex h-8 w-8 items-center justify-center rounded bg-blue-600 text-white">
                  <BookOpen className="h-4 w-4" />
                </div>
                <span className="text-xl font-bold text-blue-600">ExamPro</span>
              </div>
              <div className="hidden md:block h-6 w-px bg-gray-300"></div>
              <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
            </div>
            <div className="flex items-center gap-4">
              <button className="rounded p-2 text-gray-500 hover:bg-gray-100">
                <Bell className="h-5 w-5" />
              </button>
              <button className="rounded p-2 text-gray-500 hover:bg-gray-100">
                <HelpCircle className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="p-6">
        <div className="max-w-6xl mx-auto">
          <div className="flex gap-6">
            {/* Sidebar */}
            <div className="w-64 bg-white rounded-lg border border-gray-200 p-6">
              <nav className="space-y-2">
                {tabs.map((tab) => {
                  const Icon = tab.icon
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                        activeTab === tab.id
                          ? "bg-blue-50 text-blue-700 border border-blue-200"
                          : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                      }`}
                    >
                      <Icon className="h-4 w-4" />
                      {tab.name}
                    </button>
                  )
                })}
              </nav>
            </div>

            {/* Content */}
            <div className="flex-1">
              {activeTab === "profile" && <ProfileSettings />}
              {activeTab === "account" && (
                <AccountSettings
                  showPassword={showPassword}
                  setShowPassword={setShowPassword}
                  twoFactorEnabled={twoFactorEnabled}
                  setTwoFactorEnabled={setTwoFactorEnabled}
                />
              )}
              {activeTab === "notifications" && (
                <NotificationSettings notifications={notifications} setNotifications={setNotifications} />
              )}
              {activeTab === "appearance" && <AppearanceSettings darkMode={darkMode} setDarkMode={setDarkMode} />}
              {activeTab === "privacy" && <PrivacySettings />}
              {activeTab === "integrations" && <IntegrationSettings />}
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

function ProfileSettings() {
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Profile Settings</h2>
          <p className="text-sm text-gray-600">Manage your personal information and profile visibility</p>
        </div>
        <button className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
          <Save className="h-4 w-4" />
          Save Changes
        </button>
      </div>

      <div className="space-y-6">
        {/* Profile Picture */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">Profile Picture</label>
          <div className="flex items-center gap-4">
            <div className="flex h-20 w-20 items-center justify-center rounded-full bg-blue-100 text-blue-600 text-2xl font-medium">
              SW
            </div>
            <div>
              <button className="inline-flex items-center gap-2 bg-gray-100 text-gray-700 px-3 py-2 rounded-md hover:bg-gray-200 transition-colors text-sm">
                <Camera className="h-4 w-4" />
                Change Photo
              </button>
              <p className="text-xs text-gray-500 mt-1">JPG, PNG up to 2MB</p>
            </div>
          </div>
        </div>

        {/* Basic Information */}
        <div className="grid grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Display Name</label>
            <input
              type="text"
              defaultValue="Dr. Sarah Wilson"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Department</label>
            <input
              type="text"
              defaultValue="Mathematics Department"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
            <input
              type="tel"
              defaultValue="+****************"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Office Location</label>
            <input
              type="text"
              defaultValue="Building A, Room 205"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Bio */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Bio</label>
          <textarea
            rows={4}
            defaultValue="Professor of Mathematics with 15+ years of experience in calculus and linear algebra. Office hours: Monday & Wednesday 2-4 PM."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Office Hours */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Office Hours</label>
          <textarea
            rows={2}
            defaultValue="Monday & Wednesday: 2:00 PM - 4:00 PM&#10;Friday: 10:00 AM - 12:00 PM"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>
    </div>
  )
}

function AccountSettings({
  showPassword,
  setShowPassword,
  twoFactorEnabled,
  setTwoFactorEnabled,
}: {
  showPassword: boolean
  setShowPassword: (show: boolean) => void
  twoFactorEnabled: boolean
  setTwoFactorEnabled: (enabled: boolean) => void
}) {
  const [sessions] = useState([
    { id: 1, device: "Chrome on Windows", location: "New York, US", lastActive: "2 minutes ago", current: true },
    { id: 2, device: "Safari on iPhone", location: "New York, US", lastActive: "2 hours ago", current: false },
    { id: 3, device: "Firefox on Mac", location: "Boston, US", lastActive: "1 day ago", current: false },
  ])

  return (
    <div className="space-y-6">
      {/* Email & Password */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Account Information</h2>

        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
            <div className="flex gap-3">
              <input
                type="email"
                defaultValue="<EMAIL>"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button className="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors">
                Verify
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-1">We'll send a verification email to any new address</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Change Password</label>
            <div className="space-y-3">
              <input
                type="password"
                placeholder="Current password"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  placeholder="New password"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 pr-10"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </button>
              </div>
              <input
                type="password"
                placeholder="Confirm new password"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Two-Factor Authentication */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Two-Factor Authentication</h2>

        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="text-sm font-medium text-gray-900">Enable 2FA</h3>
            <p className="text-sm text-gray-600 mt-1">
              Add an extra layer of security to your account using an authenticator app or SMS.
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={twoFactorEnabled}
              onChange={(e) => setTwoFactorEnabled(e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </div>

        {twoFactorEnabled && (
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <div className="flex gap-3">
              <button className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-sm">
                <Smartphone className="h-4 w-4 inline mr-2" />
                Setup with App
              </button>
              <button className="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors text-sm">
                <Bell className="h-4 w-4 inline mr-2" />
                Setup with SMS
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Active Sessions */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Active Sessions</h2>

        <div className="space-y-4">
          {sessions.map((session) => (
            <div key={session.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center gap-3">
                <Monitor className="h-5 w-5 text-gray-400" />
                <div>
                  <div className="font-medium text-gray-900">{session.device}</div>
                  <div className="text-sm text-gray-600">
                    {session.location} • {session.lastActive}
                  </div>
                </div>
                {session.current && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Current session
                  </span>
                )}
              </div>
              {!session.current && (
                <button className="text-red-600 hover:text-red-700 text-sm font-medium">Revoke</button>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

function NotificationSettings({
  notifications,
  setNotifications,
}: {
  notifications: any
  setNotifications: (notifications: any) => void
}) {
  const updateNotification = (category: string, key: string, value: boolean) => {
    setNotifications((prev: any) => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value,
      },
    }))
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Notification Preferences</h2>
          <p className="text-sm text-gray-600">Choose how you want to be notified about important events</p>
        </div>
        <button className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
          <Save className="h-4 w-4" />
          Save Changes
        </button>
      </div>

      <div className="space-y-8">
        {/* Email Notifications */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Email Notifications</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-gray-900">New student registrations</div>
                <div className="text-sm text-gray-600">Get notified when students enroll in your courses</div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notifications.email.newRegistrations}
                  onChange={(e) => updateNotification("email", "newRegistrations", e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-gray-900">Submissions requiring grading</div>
                <div className="text-sm text-gray-600">Get notified when you have new submissions to grade</div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notifications.email.submissionsToGrade}
                  onChange={(e) => updateNotification("email", "submissionsToGrade", e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-gray-900">Platform announcements</div>
                <div className="text-sm text-gray-600">Get notified about new features and important updates</div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notifications.email.platformAnnouncements}
                  onChange={(e) => updateNotification("email", "platformAnnouncements", e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        </div>

        {/* In-App Notifications */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">In-App Notifications</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-gray-900">Unread messages</div>
                <div className="text-sm text-gray-600">Show notification badges for unread messages</div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notifications.inApp.unreadMessages}
                  onChange={(e) => updateNotification("inApp", "unreadMessages", e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-gray-900">Exam reminders</div>
                <div className="text-sm text-gray-600">Show reminders for upcoming scheduled exams</div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notifications.inApp.examReminders}
                  onChange={(e) => updateNotification("inApp", "examReminders", e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-gray-900">Grade updates</div>
                <div className="text-sm text-gray-600">Get notified when students view their grades</div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notifications.inApp.gradeUpdates}
                  onChange={(e) => updateNotification("inApp", "gradeUpdates", e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

function AppearanceSettings({
  darkMode,
  setDarkMode,
}: {
  darkMode: boolean
  setDarkMode: (darkMode: boolean) => void
}) {
  const [fontSize, setFontSize] = useState("medium")
  const [language, setLanguage] = useState("english")

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Appearance & Display</h2>
          <p className="text-sm text-gray-600">Customize how ExamPro looks and feels</p>
        </div>
        <button className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
          <Save className="h-4 w-4" />
          Save Changes
        </button>
      </div>

      <div className="space-y-8">
        {/* Theme */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Theme</h3>
          <div className="grid grid-cols-2 gap-4">
            <label
              className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none ${!darkMode ? "border-blue-600 bg-blue-50" : "border-gray-300 bg-white hover:bg-gray-50"}`}
            >
              <input
                type="radio"
                name="theme"
                checked={!darkMode}
                onChange={() => setDarkMode(false)}
                className="sr-only"
              />
              <div className="flex items-center">
                <Sun className="h-6 w-6 text-yellow-500 mr-3" />
                <div>
                  <div className="text-sm font-medium text-gray-900">Light Mode</div>
                  <div className="text-sm text-gray-500">Clean and bright interface</div>
                </div>
              </div>
            </label>

            <label
              className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none ${darkMode ? "border-blue-600 bg-blue-50" : "border-gray-300 bg-white hover:bg-gray-50"}`}
            >
              <input
                type="radio"
                name="theme"
                checked={darkMode}
                onChange={() => setDarkMode(true)}
                className="sr-only"
              />
              <div className="flex items-center">
                <Moon className="h-6 w-6 text-purple-500 mr-3" />
                <div>
                  <div className="text-sm font-medium text-gray-900">Dark Mode</div>
                  <div className="text-sm text-gray-500">Easy on the eyes</div>
                </div>
              </div>
            </label>
          </div>
        </div>

        {/* Font Size */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Font Size</h3>
          <div className="flex gap-4">
            {[
              { id: "small", label: "Small", size: "text-sm" },
              { id: "medium", label: "Medium", size: "text-base" },
              { id: "large", label: "Large", size: "text-lg" },
            ].map((option) => (
              <label
                key={option.id}
                className={`flex cursor-pointer rounded-lg border p-4 focus:outline-none ${
                  fontSize === option.id ? "border-blue-600 bg-blue-50" : "border-gray-300 bg-white hover:bg-gray-50"
                }`}
              >
                <input
                  type="radio"
                  name="fontSize"
                  value={option.id}
                  checked={fontSize === option.id}
                  onChange={(e) => setFontSize(e.target.value)}
                  className="sr-only"
                />
                <div className="text-center">
                  <div className={`font-medium ${option.size}`}>Aa</div>
                  <div className="text-sm text-gray-600 mt-1">{option.label}</div>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Language */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Language</h3>
          <select
            value={language}
            onChange={(e) => setLanguage(e.target.value)}
            className="w-full max-w-xs px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="english">English</option>
            <option value="spanish">Español</option>
            <option value="french">Français</option>
            <option value="german">Deutsch</option>
            <option value="chinese">中文</option>
          </select>
        </div>
      </div>
    </div>
  )
}

function PrivacySettings() {
  const [profileVisible, setProfileVisible] = useState(true)

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Privacy & Data</h2>
          <p className="text-sm text-gray-600">Control your data and privacy settings</p>
        </div>
        <button className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
          <Save className="h-4 w-4" />
          Save Changes
        </button>
      </div>

      <div className="space-y-8">
        {/* Profile Visibility */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Profile Visibility</h3>
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium text-gray-900">Show profile to other instructors</div>
              <div className="text-sm text-gray-600">
                Allow other instructors in your institution to view your profile
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={profileVisible}
                onChange={(e) => setProfileVisible(e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>

        {/* Data Download */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Data Management</h3>
          <div className="space-y-4">
            <div className="p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-gray-900">Download your data</div>
                  <div className="text-sm text-gray-600">Get a copy of all your data stored in ExamPro</div>
                </div>
                <button className="inline-flex items-center gap-2 bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors">
                  <Download className="h-4 w-4" />
                  Request Data
                </button>
              </div>
            </div>

            <div className="p-4 border border-red-200 rounded-lg bg-red-50">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-red-900">Delete your account</div>
                  <div className="text-sm text-red-700">Permanently delete your account and all associated data</div>
                </div>
                <button className="inline-flex items-center gap-2 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors">
                  <Trash2 className="h-4 w-4" />
                  Delete Account
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

function IntegrationSettings() {
  const [apiKey, setApiKey] = useState("sk-...f7g8h9i0")
  const [showApiKey, setShowApiKey] = useState(false)

  const integrations = [
    {
      name: "Canvas LMS",
      description: "Sync grades and assignments with Canvas",
      connected: true,
      logo: "C",
      color: "bg-red-500",
    },
    {
      name: "Google Classroom",
      description: "Import students and classes from Google Classroom",
      connected: false,
      logo: "G",
      color: "bg-green-500",
    },
    {
      name: "Turnitin",
      description: "Check for plagiarism in student submissions",
      connected: true,
      logo: "T",
      color: "bg-blue-500",
    },
    {
      name: "Zoom",
      description: "Schedule online proctored exams with Zoom",
      connected: false,
      logo: "Z",
      color: "bg-blue-400",
    },
  ]

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Integrations & API</h2>
          <p className="text-sm text-gray-600">Connect ExamPro with your existing tools and workflows</p>
        </div>
      </div>

      <div className="space-y-8">
        {/* API Key */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">API Access</h3>
          <div className="p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <div>
                <div className="font-medium text-gray-900">API Key</div>
                <div className="text-sm text-gray-600">Use this key to access the ExamPro API</div>
              </div>
              <button
                onClick={() => setShowApiKey(!showApiKey)}
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                {showApiKey ? "Hide" : "Show"}
              </button>
            </div>
            <div className="flex gap-3">
              <input
                type={showApiKey ? "text" : "password"}
                value={apiKey}
                readOnly
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm font-mono"
              />
              <button className="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors">
                Copy
              </button>
              <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                Regenerate
              </button>
            </div>
          </div>
        </div>

        {/* External Integrations */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">External Integrations</h3>
          <div className="space-y-4">
            {integrations.map((integration, index) => (
              <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center gap-3">
                  <div
                    className={`flex h-10 w-10 items-center justify-center rounded-lg text-white font-medium ${integration.color}`}
                  >
                    {integration.logo}
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{integration.name}</div>
                    <div className="text-sm text-gray-600">{integration.description}</div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  {integration.connected && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Connected
                    </span>
                  )}
                  <button
                    className={`inline-flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
                      integration.connected
                        ? "bg-red-100 text-red-700 hover:bg-red-200"
                        : "bg-blue-600 text-white hover:bg-blue-700"
                    }`}
                  >
                    {integration.connected ? (
                      <>
                        <X className="h-4 w-4" />
                        Disconnect
                      </>
                    ) : (
                      <>
                        <LinkIcon className="h-4 w-4" />
                        Connect
                      </>
                    )}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
