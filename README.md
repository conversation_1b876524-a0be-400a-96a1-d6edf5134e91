# SmartOnline - University Management System

A comprehensive university management platform built with Next.js 15, Prisma, and Sanity CMS. The system provides features for university administration, student management, payment processing, and subscription handling.

## 🌟 Features

### Administrative Features
- **University Management**
  - Create and manage universities
  - Department and course management
  - Instructor token generation
  - Student enrollment tracking
  - Logo and branding customization

### Student Features
- **Student Portal**
  - Personal dashboard
  - Course enrollment
  - Profile management
  - Exam scheduling
  - Result tracking
  - Question bank access

### Payment & Subscription
- **Multiple Payment Gateways**
  - Flutterwave integration
  - MTN Mobile Money
  - Orange Money
  - PayPal support
- **Subscription Management**
  - Multiple plan options
  - Automated renewal reminders
  - Usage tracking
  - Coupon system

### Security & Authentication
- JWT-based authentication
- Role-based access control
- Email verification
- Secure password handling

## 🛠 Tech Stack

- **Frontend**: Next.js 15.0.1
- **Styling**: TailwindCSS
- **State Management**: Zustand
- **Database**: PostgreSQL with Prisma ORM
- **CMS**: Sanity
- **Authentication**: JWT with jose
- **Email**: Nodemailer
- **Charts**: Chart.js with react-chartjs-2
- **UI Components**: 
  - Radix UI
  - Heroicons
  - Lucide React
- **Development**:
  - TypeScript
  - ESLint
  - PostCSS

## 📋 Prerequisites

- Node.js >= 18
- PostgreSQL database
- Sanity account (for CMS)
- Payment gateway accounts (for production)

## 🚀 Getting Started

1. **Clone the repository**
```bash
git clone <repository-url>
cd smartonline
```

2. **Install dependencies**
```bash
npm install
```

3. **Environment Setup**
Create a `.env` file with:
```env
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/smartonline"

# Authentication
JWT_SECRET="your-secure-jwt-secret"

# Email
SMTP_HOST="your-smtp-host"
SMTP_PORT="587"
SMTP_USER="your-smtp-username"
SMTP_PASS="your-smtp-password"

# Sanity
NEXT_PUBLIC_SANITY_PROJECT_ID="your-sanity-project-id"
NEXT_PUBLIC_SANITY_DATASET="production"

# Payment Gateways
FLUTTERWAVE_PUBLIC_KEY="your-flutterwave-public-key"
FLUTTERWAVE_SECRET_KEY="your-flutterwave-secret-key"
PAYPAL_CLIENT_ID="your-paypal-client-id"
PAYPAL_CLIENT_SECRET="your-paypal-client-secret"
```

4. **Database Setup**
```bash
npx prisma generate
npx prisma db push
```

5. **Start Development Server**
```bash
npm run dev
```

Visit `http://localhost:3000` to see the application.

## 📁 Project Structure

```
smartonline/
├── app/                    # Next.js app directory
│   ├── admin/             # Admin dashboard
│   ├── api/               # API routes
│   ├── component/         # Shared components
│   ├── lib/              # Utility functions
│   ├── payment/          # Payment processing
│   └── univ/             # University-specific pages
├── lib/                   # Core business logic
├── prisma/               # Database schema
├── public/              # Static assets
├── sanity/             # CMS configuration
└── utils/              # Helper utilities
```

## 🔐 Authentication System

The system implements a multi-tenant authentication system:

1. **Admin Authentication**
   - JWT-based authentication
   - Session management
   - Role-based access control

2. **Student Authentication**
   - University-specific login
   - Course access control
   - Token-based verification

3. **Instructor Authentication**
   - Department-specific access
   - Course management permissions
   - Token generation capabilities

## 💳 Payment Integration

### Supported Payment Methods
- Flutterwave (Cards, Bank transfers)
- Mobile Money (MTN, Orange)
- PayPal (International payments)

### Subscription Plans
- Multiple tier support
- Flexible billing cycles
- Feature-based access control
- Usage limitations

## 📊 Database Schema

Key models include:
- University
- Department
- Course
- Student
- Instructor
- Admin
- Payment
- Subscription
- Token

Refer to `prisma/schema.prisma` for complete schema details.

## 🚀 Deployment

### Production Build
```bash
npm run build
npm start
```

### Deployment Platforms
- Vercel (Recommended)
- AWS
- DigitalOcean
- Any Node.js hosting

## 🧪 Testing

```bash
# Run tests
npm test

# Lint code
npm run lint
```

## 📝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Support

For support:
- Create an issue
- Contact <EMAIL>
- Visit our documentation site

## 🙏 Acknowledgments

- Next.js team for the amazing framework
- Prisma team for the robust ORM
- Sanity team for the headless CMS
- All contributors to this project
