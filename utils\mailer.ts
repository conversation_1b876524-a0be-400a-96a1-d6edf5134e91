import nodemailer from 'nodemailer';

export async function sendReceiptEmail(
  email: string,
  token: string,
  amount: number,
  method: string,
  subscriptionPlan: string
) {
  const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: process.env.EMAIL_USER!,
      pass: process.env.EMAIL_PASS!,
    },
  });

  const formattedAmount = amount.toLocaleString('en-US', { style: 'currency', currency: 'XAF' });

  const mailOptions = {
    from: process.env.EMAIL_USER!,
    to: email,
    subject: 'Payment Receipt - Subscription Confirmation',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <h2 style="color: #2F855A;">Azure Hotel Subscription Receipt</h2>
        <p>Thank you for your payment. Here are your subscription details:</p>
        <table style="width: 100%; border-collapse: collapse;">
          <tbody>
            <tr>
              <td style="padding: 8px; border: 1px solid #ddd;">Subscription Plan</td>
              <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold; text-transform: capitalize;">${subscriptionPlan}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border: 1px solid #ddd;">Amount Paid</td>
              <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">${formattedAmount}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border: 1px solid #ddd;">Payment Method</td>
              <td style="padding: 8px; border: 1px solid #ddd;">${method}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border: 1px solid #ddd;">Token</td>
              <td style="padding: 8px; border: 1px solid #ddd; font-family: monospace;">${token}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border: 1px solid #ddd;">Date</td>
              <td style="padding: 8px; border: 1px solid #ddd;">${new Date().toLocaleString()}</td>
            </tr>
          </tbody>
        </table>
        <p style="margin-top: 20px;">Please keep this receipt for your records.</p>
        <p>Regards,<br/>Azure Hotel Team</p>
      </div>
    `,
  };

  await transporter.sendMail(mailOptions);
}

export async function sendRenewalReminderEmail(
  email: string,
  customerName: string,
  planName: string,
  expiryDate: Date,
  daysRemaining: number
) {
  const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: process.env.EMAIL_USER!,
      pass: process.env.EMAIL_PASS!,
    },
  });

  const formattedDate = expiryDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  const urgencyColor = daysRemaining <= 1 ? '#DC2626' : daysRemaining <= 3 ? '#F59E0B' : '#2563EB';
  const urgencyText = daysRemaining <= 1 ? 'URGENT' : daysRemaining <= 3 ? 'IMPORTANT' : 'REMINDER';

  const mailOptions = {
    from: process.env.EMAIL_USER!,
    to: email,
    subject: `${urgencyText}: Your subscription expires in ${daysRemaining} day${daysRemaining > 1 ? 's' : ''}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <div style="background-color: ${urgencyColor}; color: white; padding: 15px; border-radius: 5px; margin-bottom: 20px; text-align: center;">
          <h2 style="margin: 0; color: white;">${urgencyText}: Subscription Expiring Soon</h2>
        </div>

        <p>Dear ${customerName},</p>

        <p>This is a friendly reminder that your <strong>${planName}</strong> subscription will expire in <strong>${daysRemaining} day${daysRemaining > 1 ? 's' : ''}</strong>.</p>

        <div style="background-color: #F3F4F6; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #374151;">Subscription Details:</h3>
          <p><strong>Plan:</strong> ${planName}</p>
          <p><strong>Expiry Date:</strong> ${formattedDate}</p>
          <p><strong>Days Remaining:</strong> ${daysRemaining}</p>
        </div>

        <p>To continue enjoying uninterrupted access to our services, please renew your subscription before it expires.</p>

        <div style="text-align: center; margin: 30px 0;">
          <a href="${process.env.NEXT_PUBLIC_BASE_URL}/payment"
             style="background-color: #2F855A; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
            Renew Subscription Now
          </a>
        </div>

        <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>

        <p>Thank you for being a valued customer!</p>

        <p>Best regards,<br/>Azure Hotel Team</p>
      </div>
    `,
  };

  await transporter.sendMail(mailOptions);
}
