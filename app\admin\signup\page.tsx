"use client"

import type React from "react"
import { useRouter } from 'next/navigation';
import Link from "next/link"
import { EyeOff, Mail, Lock, User, ArrowRight, Check, Key, ArrowLeft, Shield, Building, Phone, Loader } from "lucide-react"
import { useState } from "react"
import Loading from "./loading";


export default function AdminSignupPage() {
  const [currentStep, setCurrentStep] = useState<"token" | "signup">("signup")
  const [token, setToken] = useState("")
  const [isValidating, setIsValidating] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const handleTokenSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsValidating(true)

    // Simulate token validation
    await new Promise((resolve) => setTimeout(resolve, 1500))

    // For demo purposes, accept any token that's at least 6 characters
    if (token.length >= 6) {
      setCurrentStep("signup")
    } else {
      alert("Invalid token. Please enter a valid admin creation token.")
    }

    setIsValidating(false)
  }

  const handleBackToToken = () => {
    setCurrentStep("token")
    setToken("")
  }

  const [formData, setFormData] = useState({
      name: "",
      email: "",
      Phone: "",
      password: "",
      confirmPassword: "",
      agreeToTerms: false,
    })
    const [error, setError] = useState<string | null>(null)
    const [loading, setLoading] = useState(false)
    const router = useRouter()

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value, type, checked } = e.target
        setFormData((prev) => ({
          ...prev,
          [name]: type === "checkbox" ? checked : value,
        }))
      }
    
      const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setError(null)
        const { name, email, Phone,password, confirmPassword, agreeToTerms } = formData
    // Validation
    if (!name || !Phone ||!email || !password || !confirmPassword) {
      console.log('Please fill in all required fields.')
      return
    }
    if (password !== confirmPassword) {
      console.log('Passwords do not match.')
      return
    }
    if (password.length < 8) {
      console.log('Password must be at least 8 characters.')
      return
    }
    if (!agreeToTerms) {
      console.log('You must agree to the terms and privacy policy.')
      return
    }
    console.log("submitting")
    setLoading(true)
    try {
      const res = await fetch('/api/admin/signup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name, email, phoneNumber:Phone ,password }),
      })
      if (!res.ok) {
        const err = await res.json()
        throw new Error(err.message || 'Registration failed')
      }
      // Redirect on success
      router.push('/admin/login')
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }


  return (
    <div className="min-h-screen bg-gray-50 flex overflow-hidden">
      {/* Left Side - Forms Container */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-md w-full">
          {/* Forms Wrapper with Animation */}
          <div className="relative w-full">
            

            {/* Signup Form */}
            <div
              className={`w-full transition-transform duration-500 ease-in-out `}
            >
              <div className="space-y-8">
                {/* Back Button */}
                <div className="text-center">
                  <button
                    onClick={handleBackToToken}
                    className="inline-flex items-center text-sm font-medium text-green-600 hover:text-green-500 mb-6"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Token
                  </button>
                </div>

                {/* Logo and Header */}
                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-6">
                    <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-600 text-white">
                      <Shield className="h-6 w-6" />
                    </div>
                    <span className="text-2xl font-bold text-green-600">ExamPro Admin</span>
                  </div>

                  {/* Token Success Indicator */}
                  <div className="bg-green-50 rounded-lg p-3 mb-6">
                    <div className="flex items-center justify-center gap-2">
                      <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-100">
                        <Check className="h-4 w-4 text-green-600" />
                      </div>
                      <span className="text-sm font-medium text-green-800">Token validated successfully</span>
                    </div>
                  </div>

                  <h2 className="text-3xl font-bold text-gray-900">Create admin account</h2>
                  <p className="mt-2 text-sm text-gray-600">Complete your administrator registration</p>
                </div>

                {/* Signup Form */}
                <form onSubmit={handleSubmit} className="mt-8 space-y-6">
                  <div className="space-y-4 text-black">
                    {/* Full Name Field */}
                    <div>
                      <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">
                        Full Name <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <User className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          id="fullName"
                          name="name"
                          type="text"
                          autoComplete="name"
                          required
                          className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                          placeholder="Enter your full name"
                          onChange={handleInputChange}
                        />
                      </div>
                    </div>

                    {/* Email Field */}
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        Email address <span className="text-red-500">*</span>
                      </label>
                      
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Mail className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          id="email"
                          name="email"
                          type="email"
                          autoComplete="email"
                          required
                          className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                          placeholder="Enter your email"
                          onChange={handleInputChange}
                        />
                      </div>
                    </div>
                    {/*Phone Number Field*/}
                    <div>
                      <label htmlFor="number" className="block text-sm font-medium text-gray-700 mb-1">
                        Phone Number
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Phone className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          id="number"
                          name="Phone"
                          type="text"
                          autoComplete="number"
                          required
                          className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                          placeholder="Enter your Phone Number"
                          onChange={handleInputChange}
                        />
                      </div>
                    <div/>  
                  
                         
                    {/* Admin Role Field */}
                   {/*<div>
                      <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
                        Admin Role <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Building className="h-4 w-4 text-gray-400" />
                        </div>
                        <select
                          id="role"
                          name="role"
                          required
                          className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                          onChange={handleInputChange}
                        >
                          <option value="">Select admin role</option>
                          <option value="system_admin">System Administrator</option>
                          <option value="security_admin">Security Administrator</option>
                          <option value="university_admin">University Administrator</option>
                          <option value="support_admin">Support Administrator</option>
                        </select>
                      </div>
                    </div>*/ }

                    {/* Password Field */}
                    <div>
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                        Password <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Lock className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          id="password"
                          name="password"
                          type={showPassword ? "text" : "password"}
                          autoComplete="new-password"
                          required
                          className="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                          placeholder="Create a password"
                          onChange={handleInputChange}
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          <EyeOff className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                        </button>
                      </div>
                    </div>

                    {/* Confirm Password Field */}
                    <div>
                      <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                        Confirm Password <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Lock className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          id="confirmPassword"
                          name="confirmPassword"
                          type={showConfirmPassword ? "text" : "password"}
                          autoComplete="new-password"
                          required
                          className="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                          placeholder="Confirm your password"
                          onChange={handleInputChange}
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        >
                          <EyeOff className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Password Requirements */}
                  <div className="bg-gray-50 rounded-md p-3">
                    <p className="text-xs font-medium text-gray-700 mb-2">Password must contain:</p>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-xs text-gray-600">
                        <Check className="h-3 w-3 text-green-500" />
                        <span>At least 8 characters</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-gray-600">
                        <Check className="h-3 w-3 text-green-500" />
                        <span>One uppercase letter</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-gray-600">
                        <Check className="h-3 w-3 text-green-500" />
                        <span>One number</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-gray-600">
                        <Check className="h-3 w-3 text-green-500" />
                        <span>One special character</span>
                      </div>
                    </div>
                  </div>

                  {/* Security Notice */}
                  <div className="bg-yellow-50 rounded-md p-3">
                    <p className="text-xs font-medium text-yellow-800 mb-2">Security Notice:</p>
                    <p className="text-xs text-yellow-700">
                      Administrator accounts have extensive system access. You will be required to set up two-factor
                      authentication after login.
                    </p>
                  </div>

                  {/* Terms and Privacy */}
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="terms"
                        name="agreeToTerms"
                        type="checkbox"
                        required
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                        onChange={handleInputChange}
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="terms" className="text-gray-700">
                        I agree to the{" "}
                        <Link href="/terms" className="font-medium text-green-600 hover:text-green-500">
                          Terms of Service
                        </Link>{" "}
                        and{" "}
                        <Link href="/privacy" className="font-medium text-green-600 hover:text-green-500">
                          Privacy Policy
                        </Link>
                        <span className="text-red-500">*</span>
                      </label>
                    </div>
                  </div>

                  {/* Create account button */}
                  <div>
                    <button
                    disabled = {loading}
                      className="group relative w-full disabled:bg-green-300 flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
                      type="submit"
                    >
                      Create admin account
                     {
                      loading ? <Loader className="ml-2 h-4 w-4 animate-spin duration-300"/> :<ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                     }
                    </button>
                  </div>

                  {/* Sign in link */}
                  <div className="text-center">
                    <p className="text-sm text-gray-600">
                      Already have an account?{" "}
                      <Link href="/admin/login" className="font-medium text-green-600 hover:text-green-500">
                        Sign in
                      </Link>
                    </p>
                  </div>
                 </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Image/Illustration */}
      <div className="hidden lg:block relative w-0 flex-1">
        <div className="absolute inset-0 bg-gradient-to-br from-green-400 to-green-800">
          <div className="flex items-center justify-center h-full p-12">
            <div className="text-center text-white">
              <div className="mb-8">
                <div className="inline-flex items-center justify-center w-24 h-24 bg-white/10 rounded-full mb-6">
                  {currentStep === "token" ? <Key className="h-12 w-12" /> : <Shield className="h-12 w-12" />}
                </div>
                <h2 className="text-3xl font-bold mb-4">
                  {currentStep === "token" ? "Secure Admin Access" : "Complete Administrator Setup"}
                </h2>
                <p className="text-xl text-green-100 max-w-md">
                  {currentStep === "token"
                    ? "We use secure tokens to ensure only authorized personnel can register as system administrators."
                    : "Create your administrator account to manage the entire examination platform and all its users."}
                </p>
              </div>
              <div className="grid grid-cols-1 gap-4 max-w-sm mx-auto">
                {currentStep === "token" ? (
                  <>
                    <div className="flex items-center gap-3 text-green-100">
                      <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                      <span>Enhanced security protocols</span>
                    </div>
                    <div className="flex items-center gap-3 text-green-100">
                      <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                      <span>Two-factor authentication</span>
                    </div>
                    <div className="flex items-center gap-3 text-green-100">
                      <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                      <span>Audit trail for all actions</span>
                    </div>
                    <div className="flex items-center gap-3 text-green-100">
                      <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                      <span>Role-based access control</span>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="flex items-center gap-3 text-green-100">
                      <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                      <span>Complete platform control</span>
                    </div>
                    <div className="flex items-center gap-3 text-green-100">
                      <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                      <span>University management</span>
                    </div>
                    <div className="flex items-center gap-3 text-green-100">
                      <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                      <span>Security monitoring</span>
                    </div>
                    <div className="flex items-center gap-3 text-green-100">
                      <div className="w-2 h-2 bg-green-300 rounded-full"></div>
                      <span>System configuration</span>
                    </div>
                  </>
                )} 
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
