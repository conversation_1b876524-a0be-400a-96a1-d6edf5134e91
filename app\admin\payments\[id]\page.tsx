import Link from "next/link"
import { ArrowLeft, DollarSign, CreditCard, User, Calendar, CheckCircle, XCircle, Clock, AlertCircle, Mail, Phone, Download, RefreshCw } from "lucide-react"
import prisma from "@/app/lib/prisma"
import { verifySession } from "@/app/lib/session";
import { redirect, notFound } from "next/navigation";

type PaymentDetailsType = {
  id: string;
  amount: number;
  currency: string;
  method: string;
  status: string;
  transactionId: string | null;
  email: string;
  gatewayResponse: any;
  token: string;
  createdAt: Date;
  updatedAt: Date;
  admin: {
    id: string;
    name: string;
    email: string;
    phoneNumber: string;
  } | null;
  plan: {
    id: string;
    name: string;
    price: number;
    currency: string;
    billing: string;
    features: string[];
  } | null;
  subscription: {
    id: string;
    status: string;
    activeDate: string;
  } | null;
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'completed':
      return <CheckCircle className="h-5 w-5 text-green-600" />
    case 'pending':
      return <Clock className="h-5 w-5 text-yellow-600" />
    case 'failed':
      return <XCircle className="h-5 w-5 text-red-600" />
    case 'cancelled':
      return <AlertCircle className="h-5 w-5 text-gray-600" />
    default:
      return <Clock className="h-5 w-5 text-gray-600" />
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'completed':
      return "bg-green-100 text-green-800"
    case 'pending':
      return "bg-yellow-100 text-yellow-800"
    case 'failed':
      return "bg-red-100 text-red-800"
    case 'cancelled':
      return "bg-gray-100 text-gray-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

const getMethodIcon = (method: string) => {
  switch (method.toLowerCase()) {
    case 'card':
    case 'credit_card':
      return <CreditCard className="h-5 w-5 text-blue-600" />
    case 'bank_transfer':
      return <DollarSign className="h-5 w-5 text-green-600" />
    default:
      return <DollarSign className="h-5 w-5 text-gray-600" />
  }
}

export default async function PaymentDetailsPage({ params }: { params: { id: string } }) {
  const user = await verifySession()
  if(!user) redirect('/login')
  
  // Fetch payment details
  const payment = await prisma.payment.findUnique({
    where: { id: params.id },
    select: {
      id: true,
      amount: true,
      currency: true,
      method: true,
      status: true,
      transactionId: true,
      email: true,
      gatewayResponse: true,
      token: true,
      createdAt: true,
      updatedAt: true,
      admin: {
        select: {
          id: true,
          name: true,
          email: true,
          phoneNumber: true
        }
      },
      plan: {
        select: {
          id: true,
          name: true,
          price: true,
          currency: true,
          billing: true,
          features: true
        }
      },
      subscription: {
        select: {
          id: true,
          status: true,
          activeDate: true
        }
      }
    }
  })

  if (!payment) {
    notFound()
  }

  return (
    <div className="min-h-screen w-full bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Link 
                href="/admin/payments"
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Payments
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-lg font-semibold text-gray-900">Payment Details</h1>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Main Details */}
          <div className="lg:col-span-2 space-y-6">
            
            {/* Payment Overview */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Payment Overview</h2>
                <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(payment.status)}`}>
                  {getStatusIcon(payment.status)}
                  {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Payment ID</label>
                  <p className="text-sm text-gray-900 font-mono">{payment.id}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Transaction ID</label>
                  <p className="text-sm text-gray-900 font-mono">
                    {payment.transactionId || 'Not available'}
                  </p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Amount</label>
                  <p className="text-lg font-bold text-gray-900">
                    {payment.currency} {payment.amount.toLocaleString()}
                  </p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Payment Method</label>
                  <div className="flex items-center gap-2">
                    {getMethodIcon(payment.method)}
                    <p className="text-sm text-gray-900 capitalize">
                      {payment.method.replace('_', ' ')}
                    </p>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Email</label>
                  <p className="text-sm text-gray-900">{payment.email}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Payment Token</label>
                  <p className="text-sm text-gray-900 font-mono break-all">{payment.token}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Created</label>
                  <p className="text-sm text-gray-900">
                    {payment.createdAt.toLocaleDateString()} at {payment.createdAt.toLocaleTimeString()}
                  </p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Last Updated</label>
                  <p className="text-sm text-gray-900">
                    {payment.updatedAt.toLocaleDateString()} at {payment.updatedAt.toLocaleTimeString()}
                  </p>
                </div>
              </div>
            </div>

            {/* Plan Details */}
            {payment.plan && (
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Plan Information</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Plan Name</label>
                    <p className="text-sm text-gray-900 font-medium">{payment.plan.name}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Plan Price</label>
                    <p className="text-sm text-gray-900 font-medium">
                      {payment.plan.currency} {payment.plan.price.toLocaleString()}
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Billing Cycle</label>
                    <p className="text-sm text-gray-900 capitalize">{payment.plan.billing}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Plan ID</label>
                    <p className="text-sm text-gray-900 font-mono">{payment.plan.id}</p>
                  </div>
                </div>

                {payment.plan.features && payment.plan.features.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-3">Plan Features</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {payment.plan.features.map((feature, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                          <span className="text-sm text-gray-900">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Gateway Response */}
            {payment.gatewayResponse && (
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Gateway Response</h2>
                <div className="bg-gray-50 rounded-lg p-4">
                  <pre className="text-sm text-gray-900 whitespace-pre-wrap overflow-x-auto">
                    {JSON.stringify(payment.gatewayResponse, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            
            {/* Customer Information */}
            {payment.admin && (
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Information</h3>
                
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
                      <User className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{payment.admin.name}</p>
                      <p className="text-xs text-gray-500">Administrator</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <p className="text-sm text-gray-900">{payment.admin.email}</p>
                  </div>
                  
                  {payment.admin.phoneNumber && (
                    <div className="flex items-center gap-3">
                      <Phone className="h-4 w-4 text-gray-400" />
                      <p className="text-sm text-gray-900">{payment.admin.phoneNumber}</p>
                    </div>
                  )}
                </div>

                <div className="mt-6 pt-6 border-t border-gray-200">
                  <Link
                    href={`/admin/users/${payment.admin.id}`}
                    className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                  >
                    View Customer Profile
                  </Link>
                </div>
              </div>
            )}

            {/* Subscription Information */}
            {payment.subscription && (
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Subscription</h3>
                
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Subscription ID</label>
                    <p className="text-sm text-gray-900 font-mono">{payment.subscription.id}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Status</label>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(payment.subscription.status)}`}>
                      {payment.subscription.status}
                    </span>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">Active Date</label>
                    <p className="text-sm text-gray-900">{payment.subscription.activeDate}</p>
                  </div>
                </div>

                <div className="mt-6 pt-6 border-t border-gray-200">
                  <Link
                    href={`/admin/subscriptions/${payment.subscription.id}`}
                    className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                  >
                    View Subscription Details
                  </Link>
                </div>
              </div>
            )}

            {/* Quick Actions */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              
              <div className="space-y-3">
                <button className="w-full flex items-center justify-center gap-2 bg-blue-50 text-blue-700 py-2 px-3 rounded-md hover:bg-blue-100 transition-colors text-sm font-medium">
                  <Download className="h-4 w-4" />
                  Download Receipt
                </button>
                
                {payment.status === 'pending' && (
                  <button className="w-full flex items-center justify-center gap-2 bg-yellow-50 text-yellow-700 py-2 px-3 rounded-md hover:bg-yellow-100 transition-colors text-sm font-medium">
                    <RefreshCw className="h-4 w-4" />
                    Refresh Status
                  </button>
                )}
                
                {payment.status === 'failed' && (
                  <button className="w-full flex items-center justify-center gap-2 bg-green-50 text-green-700 py-2 px-3 rounded-md hover:bg-green-100 transition-colors text-sm font-medium">
                    <RefreshCw className="h-4 w-4" />
                    Retry Payment
                  </button>
                )}
                
                <Link
                  href={`/admin/payments?customer=${payment.admin?.id}`}
                  className="w-full flex items-center justify-center gap-2 bg-gray-50 text-gray-700 py-2 px-3 rounded-md hover:bg-gray-100 transition-colors text-sm font-medium"
                >
                  <DollarSign className="h-4 w-4" />
                  View All Customer Payments
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
