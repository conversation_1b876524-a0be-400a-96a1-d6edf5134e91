import { defineType } from 'sanity'

export default defineType({
  name: 'land',
  title: 'Land',
  type: 'document',
  fields: [
    {
      name: 'image',
      title: 'Image',
      type: 'image',
      options: { hotspot: true },
    },
    {
      name: 'title',
      title: 'Title',
      type: 'string',
    },
    {
      name: 'price',
      title: 'Price',
      type: 'number',
    },
    {
      name: 'location',
      title: 'Location',
      type: 'string',
    },
    {
      name: 'size',
      title: 'Size',
      type: 'string',
    },
    {
      name: 'ownership',
      title: 'Ownership',
      type: 'string',
    },
    {
      name: 'accessibility',
      title: 'Accessibility',
      type: 'string',
    },
  ],
});
