import React from 'react'
import Form from './Form'
import LoginPage from './LeftForm'
import prisma from '@/app/lib/prisma'

const page = async ({params}:{params:Promise<{univID:string}>}) => {
    const {univID} = await params
    const universityData = await prisma.univ.findUnique({where:{id:univID},select:{name:true}})
    
  return (
    <LoginPage univID={univID} univName={universityData?.name || "Ict university"} />
  )
}

export default page
