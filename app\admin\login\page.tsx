import { verifySession } from '@/app/lib/session'
import { redirect } from 'next/navigation'
import React from 'react'
import AdminLoginPage from './form'
import Loading from './loading'

type Props = {}

const page = async (props: Props) => {
  const VerifySession = await verifySession()
  if(VerifySession)  {
    
    redirect('all-university')
    return <Loading />
  }
  return (
    <>
      <AdminLoginPage />
    </>
  )
}

export default page