"use client"

import { useState } from "react"
import {
  BookOpen,
  Search,
  Filter,
  Download,
  MoreHorizontal,
  Eye,
  RefreshCw,
  MessageSquare,
  FileText,
  Calendar,
  ChevronDown,
  Bell,
  HelpCircle,
  CheckCircle,
  Clock,
  AlertTriangle,
  ArrowUp,
  ArrowDown,
  BarChart2,
  <PERSON><PERSON><PERSON>,
  CheckSquare,
  Square,
  X,
} from "lucide-react"
import type { JSX } from "react"

// Mock data for exam results
const mockResults = [
  {
    id: 1,
    examTitle: "Advanced Mathematics Midterm",
    studentName: "<PERSON> Johnson",
    submissionDate: "2024-01-20T10:30:00Z",
    score: 82,
    totalPoints: 100,
    status: "graded",
    course: "Mathematics",
    timeSpent: 95, // minutes
    flagged: false,
  },
  {
    id: 2,
    examTitle: "Physics Quantum Mechanics Quiz",
    studentName: "<PERSON>",
    submissionDate: "2024-01-19T14:15:00Z",
    score: 75,
    totalPoints: 100,
    status: "graded",
    course: "Physics",
    timeSpent: 42,
    flagged: false,
  },
  {
    id: 3,
    examTitle: "Chemistry Lab Assessment",
    studentName: "<PERSON>",
    submissionDate: "2024-01-18T09:45:00Z",
    score: 68,
    totalPoints: 100,
    status: "pending",
    course: "Chemistry",
    timeSpent: 85,
    flagged: false,
  },
  {
    id: 4,
    examTitle: "Biology Final Examination",
    studentName: "David <PERSON>",
    submissionDate: "2024-01-17T16:20:00Z",
    score: 91,
    totalPoints: 100,
    status: "graded",
    course: "Biology",
    timeSpent: 110,
    flagged: false,
  },
  {
    id: 5,
    examTitle: "Computer Science Algorithms",
    studentName: "Emma Brown",
    submissionDate: "2024-01-16T11:00:00Z",
    score: 88,
    totalPoints: 100,
    status: "graded",
    course: "Computer Science",
    timeSpent: 78,
    flagged: false,
  },
  {
    id: 6,
    examTitle: "Advanced Mathematics Midterm",
    studentName: "Frank Miller",
    submissionDate: "2024-01-20T13:45:00Z",
    score: 65,
    totalPoints: 100,
    status: "graded",
    course: "Mathematics",
    timeSpent: 102,
    flagged: false,
  },
  {
    id: 7,
    examTitle: "Physics Quantum Mechanics Quiz",
    studentName: "Grace Lee",
    submissionDate: "2024-01-19T10:30:00Z",
    score: 79,
    totalPoints: 100,
    status: "graded",
    course: "Physics",
    timeSpent: 38,
    flagged: false,
  },
  {
    id: 8,
    examTitle: "Chemistry Lab Assessment",
    studentName: "Henry Taylor",
    submissionDate: "2024-01-18T15:20:00Z",
    score: null,
    totalPoints: 100,
    status: "pending",
    course: "Chemistry",
    timeSpent: 90,
    flagged: false,
  },
  {
    id: 9,
    examTitle: "Biology Final Examination",
    studentName: "Ivy Martinez",
    submissionDate: "2024-01-17T09:15:00Z",
    score: 72,
    totalPoints: 100,
    status: "graded",
    course: "Biology",
    timeSpent: 105,
    flagged: true,
  },
  {
    id: 10,
    examTitle: "Computer Science Algorithms",
    studentName: "Jack Robinson",
    submissionDate: "2024-01-16T14:30:00Z",
    score: null,
    totalPoints: 100,
    status: "pending",
    course: "Computer Science",
    timeSpent: 82,
    flagged: false,
  },
]

const courses = ["Mathematics", "Physics", "Chemistry", "Biology", "Computer Science"]
const exams = [
  "Advanced Mathematics Midterm",
  "Physics Quantum Mechanics Quiz",
  "Chemistry Lab Assessment",
  "Biology Final Examination",
  "Computer Science Algorithms",
]

type ResultStatus = "graded" | "pending" | "flagged"

export default function ResultsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [examFilter, setExamFilter] = useState("all")
  const [courseFilter, setCourseFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState<ResultStatus | "all">("all")
  const [sortField, setSortField] = useState<"date" | "score" | "name">("date")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc")
  const [selectedResults, setSelectedResults] = useState<number[]>([])
  const [showSubmission, setShowSubmission] = useState<number | null>(null)

  // Calculate statistics
  const totalResults = mockResults.length
  const pendingResults = mockResults.filter((result) => result.status === "pending").length
  const flaggedResults = mockResults.filter((result) => result.flagged).length
  const averageScore =
    mockResults.filter((result) => result.score !== null).reduce((sum, result) => sum + (result.score || 0), 0) /
    (totalResults - pendingResults)
  const passRate =
    (mockResults.filter((result) => result.score !== null && result.score >= 70).length /
      (totalResults - pendingResults)) *
    100

  // Filter and sort results
  const filteredResults = mockResults
    .filter((result) => {
      const matchesSearch =
        result.examTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
        result.studentName.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesExam = examFilter === "all" || result.examTitle === examFilter
      const matchesCourse = courseFilter === "all" || result.course === courseFilter
      const matchesStatus =
        statusFilter === "all" || (statusFilter === "flagged" ? result.flagged : result.status === statusFilter)
      return matchesSearch && matchesExam && matchesCourse && matchesStatus
    })
    .sort((a, b) => {
      if (sortField === "date") {
        return sortDirection === "desc"
          ? new Date(b.submissionDate).getTime() - new Date(a.submissionDate).getTime()
          : new Date(a.submissionDate).getTime() - new Date(b.submissionDate).getTime()
      } else if (sortField === "score") {
        // Handle null scores
        if (a.score === null && b.score === null) return 0
        if (a.score === null) return sortDirection === "desc" ? 1 : -1
        if (b.score === null) return sortDirection === "desc" ? -1 : 1

        return sortDirection === "desc" ? b.score - a.score : a.score - b.score
      } else {
        // Sort by name
        return sortDirection === "desc"
          ? b.studentName.localeCompare(a.studentName)
          : a.studentName.localeCompare(b.studentName)
      }
    })

  const handleSelectAll = () => {
    if (selectedResults.length === filteredResults.length) {
      setSelectedResults([])
    } else {
      setSelectedResults(filteredResults.map((r) => r.id))
    }
  }

  const handleSelectResult = (resultId: number) => {
    setSelectedResults((prev) => (prev.includes(resultId) ? prev.filter((id) => id !== resultId) : [...prev, resultId]))
  }

  const handleSort = (field: "date" | "score" | "name") => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("desc")
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString() + " " + date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  }

  const getStatusBadge = (status: ResultStatus, flagged: boolean) => {
    if (flagged) {
      return (
        <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <AlertTriangle className="h-3 w-3" />
          Flagged
        </span>
      )
    }

    const statusConfig = {
      graded: { color: "bg-green-100 text-green-800", icon: CheckCircle },
      pending: { color: "bg-yellow-100 text-yellow-800", icon: Clock },
    }

    const config = statusConfig[status]
    const Icon = config.icon

    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="h-3 w-3" />
        {status === "graded" ? "Graded" : "Pending"}
      </span>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="flex h-8 w-8 items-center justify-center rounded bg-blue-600 text-white">
                  <BookOpen className="h-4 w-4" />
                </div>
                <span className="text-xl font-bold text-blue-600">ExamPro</span>
              </div>
              <div className="hidden md:block h-6 w-px bg-gray-300"></div>
              <h1 className="text-2xl font-bold text-gray-900">Results</h1>
            </div>
            <div className="flex items-center gap-4">
              <button className="rounded p-2 text-gray-500 hover:bg-gray-100">
                <Bell className="h-5 w-5" />
                {pendingResults > 0 && (
                  <span className="absolute top-1 right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] font-bold text-white">
                    {pendingResults}
                  </span>
                )}
              </button>
              <button className="rounded p-2 text-gray-500 hover:bg-gray-100">
                <HelpCircle className="h-5 w-5" />
              </button>
              <button className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                <Download className="h-4 w-4" />
                Export Results
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="p-6">
        <div className="max-w-7xl mx-auto">
          {/* Stats Cards */}
          <div className="grid gap-6 md:grid-cols-4 mb-6">
            {/* Total Results */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Results</p>
                  <p className="text-2xl font-bold text-gray-900">{totalResults}</p>
                  <div className="flex items-center gap-1 text-xs font-medium text-green-600">
                    <ArrowUp className="h-3 w-3" />
                    12.5%
                  </div>
                </div>
                <div className="rounded-full bg-blue-100 p-3">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </div>

            {/* Pending Review */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending Review</p>
                  <p className="text-2xl font-bold text-gray-900">{pendingResults}</p>
                  <div className="flex items-center gap-1 text-xs font-medium text-red-600">
                    <ArrowUp className="h-3 w-3" />3 new
                  </div>
                </div>
                <div className="rounded-full bg-yellow-100 p-3">
                  <Clock className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
            </div>

            {/* Average Score */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Average Score</p>
                  <p className="text-2xl font-bold text-gray-900">{averageScore.toFixed(1)}%</p>
                  <div className="flex items-center gap-1 text-xs font-medium text-green-600">
                    <ArrowUp className="h-3 w-3" />
                    2.3%
                  </div>
                </div>
                <div className="rounded-full bg-green-100 p-3">
                  <BarChart2 className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </div>

            {/* Pass Rate */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pass Rate</p>
                  <p className="text-2xl font-bold text-gray-900">{passRate.toFixed(1)}%</p>
                  <div className="flex items-center gap-1 text-xs font-medium text-red-600">
                    <ArrowDown className="h-3 w-3" />
                    1.2%
                  </div>
                </div>
                <div className="rounded-full bg-purple-100 p-3">
                  <PieChart className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </div>
          </div>

          {/* Filters and Search */}
          <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search by exam title or student name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* Filters */}
              <div className="flex flex-wrap gap-3">
                {/* Exam Filter */}
                <div className="relative">
                  <select
                    value={examFilter}
                    onChange={(e) => setExamFilter(e.target.value)}
                    className="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Exams</option>
                    {exams.map((exam) => (
                      <option key={exam} value={exam}>
                        {exam}
                      </option>
                    ))}
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                </div>

                {/* Course Filter */}
                <div className="relative">
                  <select
                    value={courseFilter}
                    onChange={(e) => setCourseFilter(e.target.value)}
                    className="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Courses</option>
                    {courses.map((course) => (
                      <option key={course} value={course}>
                        {course}
                      </option>
                    ))}
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                </div>

                {/* Status Filter */}
                <div className="relative">
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value as ResultStatus | "all")}
                    className="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Status</option>
                    <option value="graded">Graded</option>
                    <option value="pending">Pending</option>
                    <option value="flagged">Flagged</option>
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                </div>

                {/* Date Range Filter Button */}
                <button className="inline-flex items-center gap-2 bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors">
                  <Calendar className="h-4 w-4" />
                  Date Range
                  <ChevronDown className="h-4 w-4" />
                </button>

                {/* Advanced Filter Button */}
                <button className="inline-flex items-center gap-2 bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors">
                  <Filter className="h-4 w-4" />
                  More Filters
                </button>
              </div>
            </div>
          </div>

          {/* Bulk Actions Bar */}
          {selectedResults.length > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <span className="text-sm font-medium text-blue-900">
                    {selectedResults.length} result{selectedResults.length > 1 ? "s" : ""} selected
                  </span>
                  <button onClick={() => setSelectedResults([])} className="text-blue-600 hover:text-blue-800 text-sm">
                    Clear selection
                  </button>
                </div>
                <div className="flex gap-2">
                  <button className="inline-flex items-center gap-2 bg-blue-600 text-white px-3 py-1.5 rounded-md hover:bg-blue-700 transition-colors text-sm">
                    <Download className="h-4 w-4" />
                    Download CSV
                  </button>
                  <button className="inline-flex items-center gap-2 bg-green-600 text-white px-3 py-1.5 rounded-md hover:bg-green-700 transition-colors text-sm">
                    <CheckCircle className="h-4 w-4" />
                    Mark as Reviewed
                  </button>
                  <button className="inline-flex items-center gap-2 bg-gray-600 text-white px-3 py-1.5 rounded-md hover:bg-gray-700 transition-colors text-sm">
                    <MessageSquare className="h-4 w-4" />
                    Send Feedback
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Results Summary */}
          <div className="mb-6">
            <p className="text-sm text-gray-600">
              Showing {filteredResults.length} of {mockResults.length} results
            </p>
          </div>

          {/* Results Table */}
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="text-left py-3 px-6">
                      <button onClick={handleSelectAll} className="flex items-center">
                        {selectedResults.length === filteredResults.length && filteredResults.length > 0 ? (
                          <CheckSquare className="h-4 w-4 text-blue-600" />
                        ) : (
                          <Square className="h-4 w-4 text-gray-400" />
                        )}
                      </button>
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Exam Title
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      <button
                        onClick={() => handleSort("name")}
                        className="flex items-center gap-1 hover:text-gray-700"
                      >
                        Student
                        {sortField === "name" && (
                          <span>
                            {sortDirection === "asc" ? (
                              <ArrowUp className="h-3 w-3" />
                            ) : (
                              <ArrowDown className="h-3 w-3" />
                            )}
                          </span>
                        )}
                      </button>
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      <button
                        onClick={() => handleSort("date")}
                        className="flex items-center gap-1 hover:text-gray-700"
                      >
                        Submission Date
                        {sortField === "date" && (
                          <span>
                            {sortDirection === "asc" ? (
                              <ArrowUp className="h-3 w-3" />
                            ) : (
                              <ArrowDown className="h-3 w-3" />
                            )}
                          </span>
                        )}
                      </button>
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      <button
                        onClick={() => handleSort("score")}
                        className="flex items-center gap-1 hover:text-gray-700"
                      >
                        Score
                        {sortField === "score" && (
                          <span>
                            {sortDirection === "asc" ? (
                              <ArrowUp className="h-3 w-3" />
                            ) : (
                              <ArrowDown className="h-3 w-3" />
                            )}
                          </span>
                        )}
                      </button>
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Status
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Time Spent
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {filteredResults.map((result) => (
                    <ResultRow
                      key={result.id}
                      result={result}
                      isSelected={selectedResults.includes(result.id)}
                      onSelect={() => handleSelectResult(result.id)}
                      onViewSubmission={() => setShowSubmission(result.id)}
                      formatDate={formatDate}
                      getStatusBadge={getStatusBadge}
                    />
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>

      {/* Submission Modal */}
      {showSubmission !== null && (
        <SubmissionModal
          result={mockResults.find((r) => r.id === showSubmission)!}
          onClose={() => setShowSubmission(null)}
        />
      )}
    </div>
  )
}

function ResultRow({
  result,
  isSelected,
  onSelect,
  onViewSubmission,
  formatDate,
  getStatusBadge,
}: {
  result: any
  isSelected: boolean
  onSelect: () => void
  onViewSubmission: () => void
  formatDate: (date: string) => string
  getStatusBadge: (status: ResultStatus, flagged: boolean) => JSX.Element
}) {
  const [showDropdown, setShowDropdown] = useState(false)

  return (
    <tr className="hover:bg-gray-50">
      <td className="py-4 px-6">
        <button onClick={onSelect}>
          {isSelected ? (
            <CheckSquare className="h-4 w-4 text-blue-600" />
          ) : (
            <Square className="h-4 w-4 text-gray-400" />
          )}
        </button>
      </td>
      <td className="py-4 px-6">
        <div className="text-sm font-medium text-gray-900">{result.examTitle}</div>
        <div className="text-xs text-gray-500">{result.course}</div>
      </td>
      <td className="py-4 px-6">
        <div className="flex items-center gap-3">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600 text-sm font-medium">
            {result.studentName.charAt(0)}
          </div>
          <div className="text-sm font-medium text-gray-900">{result.studentName}</div>
        </div>
      </td>
      <td className="py-4 px-6 text-sm text-gray-900">{formatDate(result.submissionDate)}</td>
      <td className="py-4 px-6">
        {result.score !== null ? (
          <div className="text-sm font-medium text-gray-900">
            {result.score}/{result.totalPoints} ({((result.score / result.totalPoints) * 100).toFixed(1)}%)
          </div>
        ) : (
          <div className="text-sm text-gray-500">Pending</div>
        )}
      </td>
      <td className="py-4 px-6">{getStatusBadge(result.status, result.flagged)}</td>
      <td className="py-4 px-6 text-sm text-gray-900">
        {Math.floor(result.timeSpent / 60)}h {result.timeSpent % 60}m
      </td>
      <td className="py-4 px-6">
        <div className="relative">
          <button
            onClick={() => setShowDropdown(!showDropdown)}
            className="p-1 rounded hover:bg-gray-100 transition-colors"
          >
            <MoreHorizontal className="h-4 w-4 text-gray-500" />
          </button>
          {showDropdown && (
            <div className="absolute right-0 top-8 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10">
              <div className="py-1">
                <button
                  onClick={() => {
                    onViewSubmission()
                    setShowDropdown(false)
                  }}
                  className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  <Eye className="h-4 w-4" />
                  View Submission
                </button>
                {result.status === "pending" && (
                  <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                    <CheckCircle className="h-4 w-4" />
                    Grade Submission
                  </button>
                )}
                {result.status === "graded" && (
                  <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                    <RefreshCw className="h-4 w-4" />
                    Regrade
                  </button>
                )}
                <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                  <MessageSquare className="h-4 w-4" />
                  Send Feedback
                </button>
                <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                  <Download className="h-4 w-4" />
                  Export PDF
                </button>
              </div>
            </div>
          )}
        </div>
      </td>
    </tr>
  )
}

function SubmissionModal({ result, onClose }: { result: any; onClose: () => void }) {
  // Mock data for the submission details
  const questions = [
    {
      id: 1,
      type: "multiple-choice",
      text: "What is the derivative of f(x) = x²?",
      options: ["f'(x) = x", "f'(x) = 2x", "f'(x) = 2", "f'(x) = x²"],
      correctAnswer: 1, // Index of correct option
      studentAnswer: 1, // Index of student's answer
      points: 5,
      earnedPoints: 5,
    },
    {
      id: 2,
      type: "multiple-choice",
      text: "Which of the following is a solution to the equation x² - 4 = 0?",
      options: ["x = 0", "x = 2", "x = 4", "x = -2"],
      correctAnswer: [1, 3], // Indices of correct options (multiple correct)
      studentAnswer: [1], // Indices of student's answers
      points: 5,
      earnedPoints: 2.5,
    },
    {
      id: 3,
      type: "essay",
      text: "Explain the concept of limits in calculus and provide an example.",
      studentAnswer:
        "A limit is the value that a function approaches as the input approaches some value. For example, the limit of (sin x)/x as x approaches 0 is 1, even though the function is undefined at x=0.",
      points: 10,
      earnedPoints: result.status === "pending" ? null : 8,
      feedback:
        result.status === "pending" ? "" : "Good explanation, but could use more detail on the formal definition.",
    },
    {
      id: 4,
      type: "true-false",
      text: "The integral of f(x) = 2x is F(x) = x² + C.",
      correctAnswer: true,
      studentAnswer: true,
      points: 5,
      earnedPoints: 5,
    },
    {
      id: 5,
      type: "multiple-choice",
      text: "Which of the following is NOT a property of a continuous function?",
      options: [
        "The function has no holes or breaks in its graph.",
        "The limit exists at every point in its domain.",
        "The function is differentiable at every point.",
        "The function value equals the limit at every point.",
      ],
      correctAnswer: 2, // Index of correct option
      studentAnswer: 0, // Index of student's answer
      points: 5,
      earnedPoints: 0,
    },
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Exam Submission</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="space-y-6">
          {/* Submission Header */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-lg font-medium text-gray-900">{result.examTitle}</h3>
                <p className="text-sm text-gray-600">{result.course}</p>
                <div className="mt-2 flex items-center gap-2">
                  <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 text-blue-600 text-xs font-medium">
                    {result.studentName.charAt(0)}
                  </div>
                  <span className="text-sm font-medium">{result.studentName}</span>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-600">Submitted on</div>
                <div className="text-sm font-medium">
                  {new Date(result.submissionDate).toLocaleDateString()}{" "}
                  {new Date(result.submissionDate).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                </div>
                <div className="mt-2 flex items-center justify-end gap-2">
                  <div className="text-sm text-gray-600">Time spent:</div>
                  <span className="text-sm font-medium">
                    {Math.floor(result.timeSpent / 60)}h {result.timeSpent % 60}m
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Score Summary */}
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-blue-700">Total Score</div>
                {result.score !== null ? (
                  <div className="text-2xl font-bold text-blue-900">
                    {result.score}/{result.totalPoints} ({((result.score / result.totalPoints) * 100).toFixed(1)}%)
                  </div>
                ) : (
                  <div className="text-2xl font-bold text-blue-900">Pending</div>
                )}
              </div>
              <div>{result.status === "pending" ? "Needs Grading" : getStatusBadge(result.status, result.flagged)}</div>
            </div>
          </div>

          {/* Questions and Answers */}
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Questions and Answers</h3>

            {questions.map((question, index) => (
              <div key={question.id} className="border border-gray-200 rounded-lg overflow-hidden">
                <div className="bg-gray-50 p-4 flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-gray-900">Question {index + 1}</span>
                    <span className="text-sm text-gray-600">
                      (
                      {question.type === "multiple-choice"
                        ? "Multiple Choice"
                        : question.type === "essay"
                          ? "Essay"
                          : "True/False"}
                      )
                    </span>
                  </div>
                  <div className="text-sm">
                    {question.earnedPoints !== null ? (
                      <span
                        className={
                          question.earnedPoints === question.points
                            ? "text-green-600 font-medium"
                            : question.earnedPoints === 0
                              ? "text-red-600 font-medium"
                              : "text-yellow-600 font-medium"
                        }
                      >
                        {question.earnedPoints}/{question.points} points
                      </span>
                    ) : (
                      <span className="text-yellow-600 font-medium">Pending/{question.points} points</span>
                    )}
                  </div>
                </div>

                <div className="p-4">
                  <div className="text-gray-900 mb-4">{question.text}</div>

                  {question.type === "multiple-choice" && (
                    <div className="space-y-2">
                      {question.options.map((option, optionIndex) => (
                        <div
                          key={optionIndex}
                          className={`p-3 rounded-md ${
                            Array.isArray(question.studentAnswer)
                              ? question.studentAnswer.includes(optionIndex)
                                ? Array.isArray(question.correctAnswer)
                                  ? question.correctAnswer.includes(optionIndex)
                                    ? "bg-green-100 border border-green-300"
                                    : "bg-red-100 border border-red-300"
                                  : question.correctAnswer === optionIndex
                                    ? "bg-green-100 border border-green-300"
                                    : "bg-red-100 border border-red-300"
                                : Array.isArray(question.correctAnswer)
                                  ? question.correctAnswer.includes(optionIndex)
                                    ? "bg-green-50 border border-green-200"
                                    : "bg-gray-50 border border-gray-200"
                                  : question.correctAnswer === optionIndex
                                    ? "bg-green-50 border border-green-200"
                                    : "bg-gray-50 border border-gray-200"
                              : question.studentAnswer === optionIndex
                                ? question.correctAnswer === optionIndex
                                  ? "bg-green-100 border border-green-300"
                                  : "bg-red-100 border border-red-300"
                                : question.correctAnswer === optionIndex
                                  ? "bg-green-50 border border-green-200"
                                  : "bg-gray-50 border border-gray-200"
                          }`}
                        >
                          <div className="flex items-start">
                            <div className="flex-shrink-0 mt-0.5">
                              {Array.isArray(question.studentAnswer) ? (
                                question.studentAnswer.includes(optionIndex) ? (
                                  <CheckSquare className="h-5 w-5 text-blue-600" />
                                ) : (
                                  <Square className="h-5 w-5 text-gray-400" />
                                )
                              ) : (
                                <div
                                  className={`h-5 w-5 rounded-full border ${
                                    question.studentAnswer === optionIndex
                                      ? "border-blue-600 bg-blue-600"
                                      : "border-gray-300"
                                  } flex items-center justify-center`}
                                >
                                  {question.studentAnswer === optionIndex && (
                                    <div className="h-2 w-2 rounded-full bg-white"></div>
                                  )}
                                </div>
                              )}
                            </div>
                            <div className="ml-3">
                              <p className="text-sm text-gray-900">{option}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {question.type === "essay" && (
                    <div>
                      <div className="bg-gray-50 p-4 rounded-md border border-gray-200 mb-4">
                        <p className="text-sm text-gray-900 whitespace-pre-line">{question.studentAnswer}</p>
                      </div>

                      {question.earnedPoints !== null ? (
                        <div className="bg-blue-50 p-4 rounded-md border border-blue-200">
                          <h4 className="text-sm font-medium text-blue-900 mb-2">Instructor Feedback</h4>
                          <p className="text-sm text-blue-800">{question.feedback}</p>
                        </div>
                      ) : (
                        <div className="bg-yellow-50 p-4 rounded-md border border-yellow-200">
                          <h4 className="text-sm font-medium text-yellow-900 mb-2">Grading Required</h4>
                          <div className="flex items-center gap-2">
                            <input
                              type="number"
                              min="0"
                              max={question.points}
                              placeholder="Points"
                              className="w-20 px-2 py-1 border border-gray-300 rounded-md"
                            />
                            <span className="text-sm text-gray-600">/ {question.points}</span>
                            <input
                              type="text"
                              placeholder="Add feedback..."
                              className="flex-1 px-3 py-1 border border-gray-300 rounded-md"
                            />
                            <button className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm">Save</button>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {question.type === "true-false" && (
                    <div className="space-y-2">
                      <div
                        className={`p-3 rounded-md ${
                          question.studentAnswer === true
                            ? question.correctAnswer === true
                              ? "bg-green-100 border border-green-300"
                              : "bg-red-100 border border-red-300"
                            : question.correctAnswer === true
                              ? "bg-green-50 border border-green-200"
                              : "bg-gray-50 border border-gray-200"
                        }`}
                      >
                        <div className="flex items-start">
                          <div className="flex-shrink-0 mt-0.5">
                            <div
                              className={`h-5 w-5 rounded-full border ${
                                question.studentAnswer === true ? "border-blue-600 bg-blue-600" : "border-gray-300"
                              } flex items-center justify-center`}
                            >
                              {question.studentAnswer === true && <div className="h-2 w-2 rounded-full bg-white"></div>}
                            </div>
                          </div>
                          <div className="ml-3">
                            <p className="text-sm text-gray-900">True</p>
                          </div>
                        </div>
                      </div>

                      <div
                        className={`p-3 rounded-md ${
                          question.studentAnswer === false
                            ? question.correctAnswer === false
                              ? "bg-green-100 border border-green-300"
                              : "bg-red-100 border border-red-300"
                            : question.correctAnswer === false
                              ? "bg-green-50 border border-green-200"
                              : "bg-gray-50 border border-gray-200"
                        }`}
                      >
                        <div className="flex items-start">
                          <div className="flex-shrink-0 mt-0.5">
                            <div
                              className={`h-5 w-5 rounded-full border ${
                                question.studentAnswer === false ? "border-blue-600 bg-blue-600" : "border-gray-300"
                              } flex items-center justify-center`}
                            >
                              {question.studentAnswer === false && (
                                <div className="h-2 w-2 rounded-full bg-white"></div>
                              )}
                            </div>
                          </div>
                          <div className="ml-3">
                            <p className="text-sm text-gray-900">False</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4 border-t">
            {result.status === "pending" ? (
              <button className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors">
                <CheckCircle className="h-4 w-4 inline mr-2" />
                Complete Grading
              </button>
            ) : (
              <button className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors">
                <RefreshCw className="h-4 w-4 inline mr-2" />
                Regrade
              </button>
            )}
            <button className="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors">
              <MessageSquare className="h-4 w-4 inline mr-2" />
              Send Feedback
            </button>
            <button className="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors">
              <Download className="h-4 w-4 inline mr-2" />
              Export PDF
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
