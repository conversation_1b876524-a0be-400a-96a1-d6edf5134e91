import prisma from "@/app/lib/prisma"
import { createSession } from "@/app/lib/session"
import { NextResponse } from "next/server"
import bcrypt from 'bcrypt'
export async function POST(request:Request) {
    const {email,password} = await request.json()

    try {
        const existingUser = await prisma.admin.findUnique({
            where:{
                email:email
            }})
        if(!existingUser) return NextResponse.json({message:"User not found"},{status:404})
        const res = await bcrypt.compare(password,existingUser.password)
        if(!res) return NextResponse.json({message:"Invalid Credential"},{status:400})
        await createSession({
            id:existingUser.id,
            email:existingUser.email,
            role:'admin'
        })
        return NextResponse.json({message:"Login successful"},{status:200})
    } catch (error) {
        console.log(error)
        return NextResponse.json({message:"Something went wrong"},{status:500})
    }
}