@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
  }

  body {
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  button {
    cursor: pointer;
    transition: all 0.2s ease-in-out;
  }

  button:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  input:focus {
    outline: none;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus-visible:ring-blue-500;
  }

  .btn-secondary {
    @apply bg-gray-100 text-gray-900 hover:bg-gray-200 focus-visible:ring-gray-500;
  }

  .card {
    @apply rounded-lg border border-gray-200 bg-white shadow-sm;
  }
}
