'use client';

import { useState } from 'react';
import Link from 'next/link';
import Input, { ErrorType } from './Input';
import {Lock, Mail} from 'lucide-react'
import { ErrorValidation } from './interface';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState({email:false, password:false});
  const [success, setSuccess] = useState('');

  interface LoginFormEvent extends React.FormEvent<HTMLFormElement> {}

  const handleLogin = (e: LoginFormEvent) => {
    e.preventDefault();

    if (error.email || error.password) {
      console.log(email,password,"error ")
      return;
    }

    
    setSuccess('Login successful!');
    console.log('Logging in with:', email, password);
  };
const paswwordError:ErrorValidation={
  type: {
    notEmpty: true,
    characterLimit: { min: 3, max: 10 },
    NoSpecailcharaterType: "NoSpecialCharacter"
  }
}
  return (
    <div style={styles.container}>
      <form onSubmit={handleLogin} style={styles.form}>
        <h2 style={styles.heading}>Login</h2>

        {success && <p style={styles.success}>{success}</p>}

        <div style={styles.inputGroup}>
          <label>Email:</label>
          <Input
          name='email'
          type='email'
          OnChange={(e) => setEmail(e)}
          placeholder='Enter your email'
          icon={<Mail/>}
          error={null}
          value={email}
          />
        </div>

        <div style={styles.inputGroup}>
          <label>Password:</label>
          <Input
          name='password'
          error={paswwordError}
          setError={(e) => setError({...error, password: e})}
            type="password"
            value={password}
            OnChange={(e) => setPassword(e)}
            placeholder="Enter your password"
            icon={<Lock />}
          />
        </div>

        <button type="submit" disabled={error.email || error.password} className='disabled:cursor-not-allowed' style={styles.button}>Log In</button>

        <div style={styles.links}>
          <p>
            Don’t have an account? <Link href="/signup" style={styles.link}>Create one</Link>
          </p>
          <p>
            <Link href="/forgot-password" style={styles.link}>Forgot password?</Link>
          </p>
        </div>
      </form>
    </div>
  );
}

const styles = {
  container: {
    minHeight: '100vh',
    color:'black',
    backgroundColor: 'lightgray', // page background
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  form: {
    backgroundColor: 'white', // form box
    padding: '30px',
    borderRadius: '10px',
    boxShadow: '0 4px 12px gray',
    width: '100%',
    maxWidth: '400px',
  },
  heading: {
    textAlign: 'center' as const,
    marginBottom: '20px',
    color: 'black',
  },
  inputGroup: {
    marginBottom: '15px',
  },
  button: {
    width: '100%',
    padding: '12px',
    backgroundColor: 'skyblue', // button background
    color: 'white',
    fontWeight: 'bold',
    border: 'none',
    borderRadius: '5px',
    cursor: 'pointer',
    marginTop: '10px',
  },
  links: {
    textAlign: 'center' as const,
    marginTop: '15px',
    fontSize: '14px',
    color: 'black',
  },
  link: {
    color: 'blue', // link color
    textDecoration: 'underline',
    cursor: 'pointer',
  },
  success: {
    color: 'green',
    marginBottom: '10px',
    textAlign: 'center' as const,
  },
};
