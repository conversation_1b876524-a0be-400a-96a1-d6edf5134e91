import prisma from "@/app/lib/prisma"
import { NextResponse } from "next/server"
import bcrypt from "bcrypt"

export async function POST(request:Request) {

    const  {univID,name,email,password} = await request.json()
    const univDomain = await prisma.univ.findUnique({where:{id:univID},select:{domain:true}})  
    if(!univDomain) return NextResponse.json({message:"University not existing or domain not found"},{status:404})
    if(univDomain.domain !== email.split('@')[1]) return NextResponse.json({message:`Invalid email  domain use the domain of ${univDomain.domain} `},{status:400})
    
    const existingUser = await prisma.student.findUnique({where:{email:email}})
    if(existingUser) return NextResponse.json({message:"User already exists"},{status:400})
    try {
        const hashedPassword = await bcrypt.hash(password,10)
        await prisma.student.create({
            data:{
                name,
                email,
                password:hashedPassword,
                univ:{
                    connect:{ id:univID }
                }
            },

        })
        return NextResponse.json({message:"Account created Succesfully"},{status:200})
    } catch (error) {
        return NextResponse.json({message:"Something went wrong"},{status:500})
    }
}