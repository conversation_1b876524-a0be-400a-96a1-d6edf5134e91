import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";
import bcrypt from "bcrypt";

export async function POST(request: Request) {
    const { email, password } = await request.json();
    try {
        const user = await prisma.user.findUnique({
            where: {
                email,
            }
        });
        if (!user) {
            return NextResponse.json({
                message: "User not found."
            }, {
                status: 404
            })
        }
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            return NextResponse.json({
                message: "Invalid password."
            }, {
                status: 401
            })
        }
        return NextResponse.json({
            message: "Login successful."
        }, {
            status: 200
        })
    } catch (error) {
        console.log(error);
        return NextResponse.json({
            message: "<PERSON><PERSON> failed."
        }, {
        status: 500,
        })
    }
}