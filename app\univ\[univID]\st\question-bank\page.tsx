"use client"

import { useState, useEffect } from "react"
import {
  Book<PERSON>pen,
  Search,
  Plus,
  Upload,
  Download,
  MoreHorizontal,
  Eye,
  Edit,
  Copy,
  Trash2,
  Tag,
  ChevronDown,
  Bell,
  HelpCircle,
  CheckSquare,
  Square,
  X,
  FileText,
  MessageCircle,
  ToggleLeft,
  List,
  Hash,
  Loader2,
} from "lucide-react"
import { QuestionService } from "@/lib/question-service"

// Mock data for questions
const mockQuestions = [
  {
    id: "Q-1024",
    title: "Derivative of x²",
    text: "What is the derivative of f(x) = x²?",
    type: "multiple-choice",
    subject: "Calculus",
    difficulty: "Easy",
    lastEdited: "2024-01-20",
    tags: ["Derivatives", "Basic Calculus"],
    options: ["f'(x) = x", "f'(x) = 2x", "f'(x) = 2", "f'(x) = x²"],
    correctAnswer: 1,
  },
  {
    id: "Q-1025",
    title: "Limits Concept Essay",
    text: "Explain the concept of limits in calculus and provide an example.",
    type: "essay",
    subject: "<PERSON><PERSON>",
    difficulty: "Hard",
    lastEdited: "2024-01-19",
    tags: ["Limits", "Theory"],
    wordLimit: 300,
  },
  {
    id: "Q-1026",
    title: "Continuous Function Property",
    text: "A continuous function has no breaks or holes in its graph.",
    type: "true-false",
    subject: "Calculus",
    difficulty: "Medium",
    lastEdited: "2024-01-18",
    tags: ["Continuity", "Functions"],
    correctAnswer: true,
  },
  {
    id: "Q-1027",
    title: "Integral Fill-in",
    text: "The integral of 2x is _____ + C.",
    type: "fill-blank",
    subject: "Calculus",
    difficulty: "Easy",
    lastEdited: "2024-01-17",
    tags: ["Integration", "Basic Calculus"],
    acceptableAnswers: ["x²", "x^2", "x squared"],
  },
  {
    id: "Q-1028",
    title: "Function Matching",
    text: "Match the functions with their derivatives:",
    type: "matching",
    subject: "Calculus",
    difficulty: "Medium",
    lastEdited: "2024-01-16",
    tags: ["Derivatives", "Functions"],
    pairs: [
      { left: "x³", right: "3x²" },
      { left: "sin(x)", right: "cos(x)" },
      { left: "e^x", right: "e^x" },
    ],
  },
]

const questionTypes = ["multiple-choice", "essay", "true-false", "fill-blank", "matching"]
const subjects = ["Calculus", "Algebra", "Geometry", "Statistics", "Physics"]
const difficulties = ["Easy", "Medium", "Hard"]

export default function QuestionBankPage({ params }: { params: { univID: string } }) {
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [subjectFilter, setSubjectFilter] = useState("all")
  const [difficultyFilter, setDifficultyFilter] = useState("all")
  const [selectedQuestions, setSelectedQuestions] = useState<string[]>([])
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showImportModal, setShowImportModal] = useState(false)
  const [selectedQuestion, setSelectedQuestion] = useState<any>(null)
  const [questionType, setQuestionType] = useState("multiple-choice")

  // Real data state
  const [questions, setQuestions] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [total, setTotal] = useState(0)

  // Fetch questions from database
  const fetchQuestions = async () => {
    try {
      setLoading(true)
      const filter = {
        univId: params.univID,
        search: searchTerm || undefined,
        type: typeFilter !== "all" ? typeFilter : undefined,
        subject: subjectFilter !== "all" ? subjectFilter : undefined,
        difficulty: difficultyFilter !== "all" ? difficultyFilter : undefined,
      }

      const result = await QuestionService.getQuestions(filter, currentPage, 20)
      setQuestions(result.questions)
      setTotalPages(result.totalPages)
      setTotal(result.total)
    } catch (error) {
      console.error('Failed to fetch questions:', error)
      setQuestions([])
    } finally {
      setLoading(false)
    }
  }

  // Fetch questions on component mount and when filters change
  useEffect(() => {
    fetchQuestions()
  }, [params.univID, searchTerm, typeFilter, subjectFilter, difficultyFilter, currentPage])

  const filteredQuestions = questions

  const handleSelectAll = () => {
    if (selectedQuestions.length === filteredQuestions.length) {
      setSelectedQuestions([])
    } else {
      setSelectedQuestions(filteredQuestions.map((q) => q.id))
    }
  }

  const handleSelectQuestion = (questionId: string) => {
    setSelectedQuestions((prev) =>
      prev.includes(questionId) ? prev.filter((id) => id !== questionId) : [...prev, questionId],
    )
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "multiple-choice":
        return <List className="h-4 w-4" />
      case "essay":
        return <FileText className="h-4 w-4" />
      case "true-false":
        return <ToggleLeft className="h-4 w-4" />
      case "fill-blank":
        return <Hash className="h-4 w-4" />
      case "matching":
        return <MessageCircle className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Easy":
        return "bg-green-100 text-green-800"
      case "Medium":
        return "bg-yellow-100 text-yellow-800"
      case "Hard":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="flex h-8 w-8 items-center justify-center rounded bg-blue-600 text-white">
                  <BookOpen className="h-4 w-4" />
                </div>
                <span className="text-xl font-bold text-blue-600">ExamPro</span>
              </div>
              <div className="hidden md:block h-6 w-px bg-gray-300"></div>
              <h1 className="text-2xl font-bold text-gray-900">Question Bank</h1>
            </div>
            <div className="flex items-center gap-4">
              <button className="rounded p-2 text-gray-500 hover:bg-gray-100">
                <Bell className="h-5 w-5" />
              </button>
              <button className="rounded p-2 text-gray-500 hover:bg-gray-100">
                <HelpCircle className="h-5 w-5" />
              </button>
              <div className="flex gap-2">
                <button
                  onClick={() => setShowImportModal(true)}
                  className="inline-flex items-center gap-2 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors font-medium"
                >
                  <Upload className="h-4 w-4" />
                  Import
                </button>
                <button
                  onClick={() => setShowCreateModal(true)}
                  className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  <Plus className="h-4 w-4" />
                  Add Question
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="p-6">
        <div className="max-w-7xl mx-auto">
          {/* Filters and Search */}
          <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search questions by title, content, or ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* Filters */}
              <div className="flex flex-wrap gap-3">
                {/* Type Filter */}
                <div className="relative">
                  <select
                    value={typeFilter}
                    onChange={(e) => setTypeFilter(e.target.value)}
                    className="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Types</option>
                    <option value="multiple-choice">Multiple Choice</option>
                    <option value="essay">Essay</option>
                    <option value="true-false">True/False</option>
                    <option value="fill-blank">Fill in the Blank</option>
                    <option value="matching">Matching</option>
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                </div>

                {/* Subject Filter */}
                <div className="relative">
                  <select
                    value={subjectFilter}
                    onChange={(e) => setSubjectFilter(e.target.value)}
                    className="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Subjects</option>
                    {subjects.map((subject) => (
                      <option key={subject} value={subject}>
                        {subject}
                      </option>
                    ))}
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                </div>

                {/* Difficulty Filter */}
                <div className="relative">
                  <select
                    value={difficultyFilter}
                    onChange={(e) => setDifficultyFilter(e.target.value)}
                    className="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Difficulties</option>
                    {difficulties.map((difficulty) => (
                      <option key={difficulty} value={difficulty}>
                        {difficulty}
                      </option>
                    ))}
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                </div>

                {/* Export Button */}
                <button className="inline-flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors font-medium">
                  <Download className="h-4 w-4" />
                  Export
                </button>
              </div>
            </div>
          </div>

          {/* Bulk Actions Bar */}
          {selectedQuestions.length > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <span className="text-sm font-medium text-blue-900">
                    {selectedQuestions.length} question{selectedQuestions.length > 1 ? "s" : ""} selected
                  </span>
                  <button
                    onClick={() => setSelectedQuestions([])}
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    Clear selection
                  </button>
                </div>
                <div className="flex gap-2">
                  <button className="inline-flex items-center gap-2 bg-blue-600 text-white px-3 py-1.5 rounded-md hover:bg-blue-700 transition-colors text-sm">
                    <Tag className="h-4 w-4" />
                    Add Tags
                  </button>
                  <button className="inline-flex items-center gap-2 bg-green-600 text-white px-3 py-1.5 rounded-md hover:bg-green-700 transition-colors text-sm">
                    <Download className="h-4 w-4" />
                    Export Selected
                  </button>
                  <button className="inline-flex items-center gap-2 bg-red-600 text-white px-3 py-1.5 rounded-md hover:bg-red-700 transition-colors text-sm">
                    <Trash2 className="h-4 w-4" />
                    Delete
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Results Summary */}
          <div className="mb-6">
            <p className="text-sm text-gray-600">
              Showing {filteredQuestions.length} of {total} questions
              {totalPages > 1 && (
                <span className="ml-2">
                  (Page {currentPage} of {totalPages})
                </span>
              )}
            </p>
          </div>

          {/* Questions Table */}
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="text-left py-3 px-6">
                      <button onClick={handleSelectAll} className="flex items-center">
                        {selectedQuestions.length === filteredQuestions.length && filteredQuestions.length > 0 ? (
                          <CheckSquare className="h-4 w-4 text-blue-600" />
                        ) : (
                          <Square className="h-4 w-4 text-gray-400" />
                        )}
                      </button>
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Question ID
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Title & Preview
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Type
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Subject
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Difficulty
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Last Edited
                    </th>
                    <th className="text-left py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {loading ? (
                    <tr>
                      <td colSpan={7} className="py-12 text-center">
                        <div className="flex items-center justify-center">
                          <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
                          <span className="ml-2 text-gray-500">Loading questions...</span>
                        </div>
                      </td>
                    </tr>
                  ) : filteredQuestions.length === 0 ? (
                    <tr>
                      <td colSpan={7} className="py-12 text-center">
                        <div className="text-gray-500">
                          <BookOpen className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                          <p className="text-lg font-medium">No questions found</p>
                          <p className="text-sm">Try adjusting your search or filters</p>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    filteredQuestions.map((question) => (
                      <QuestionRow
                        key={question.id}
                        question={question}
                        isSelected={selectedQuestions.includes(question.id)}
                        onSelect={() => handleSelectQuestion(question.id)}
                        onPreview={() => setSelectedQuestion(question)}
                        getTypeIcon={getTypeIcon}
                        getDifficultyColor={getDifficultyColor}
                      />
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>

      {/* Modals */}
      {showCreateModal && (
        <CreateQuestionModal
          questionType={questionType}
          setQuestionType={setQuestionType}
          onClose={() => setShowCreateModal(false)}
        />
      )}
      {showImportModal && <ImportModal onClose={() => setShowImportModal(false)} />}
      {selectedQuestion && <PreviewModal question={selectedQuestion} onClose={() => setSelectedQuestion(null)} />}
    </div>
  )
}

function QuestionRow({
  question,
  isSelected,
  onSelect,
  onPreview,
  getTypeIcon,
  getDifficultyColor,
}: {
  question: any
  isSelected: boolean
  onSelect: () => void
  onPreview: () => void
  getTypeIcon: (type: string) => JSX.Element
  getDifficultyColor: (difficulty: string) => string
}) {
  const [showDropdown, setShowDropdown] = useState(false)

  return (
    <tr className="hover:bg-gray-50">
      <td className="py-4 px-6">
        <button onClick={onSelect}>
          {isSelected ? (
            <CheckSquare className="h-4 w-4 text-blue-600" />
          ) : (
            <Square className="h-4 w-4 text-gray-400" />
          )}
        </button>
      </td>
      <td className="py-4 px-6">
        <div className="text-sm font-mono font-medium text-gray-900">{question.id}</div>
      </td>
      <td className="py-4 px-6">
        <div>
          <div className="text-sm font-medium text-gray-900">{question.title}</div>
          <div className="text-sm text-gray-600 truncate max-w-xs">{question.text}</div>
          <div className="flex flex-wrap gap-1 mt-1">
            {question.tags.map((tag: string) => (
              <span
                key={tag}
                className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
              >
                {tag}
              </span>
            ))}
          </div>
        </div>
      </td>
      <td className="py-4 px-6">
        <div className="flex items-center gap-2">
          {getTypeIcon(question.type)}
          <span className="text-sm text-gray-900 capitalize">{question.type.replace("-", " ")}</span>
        </div>
      </td>
      <td className="py-4 px-6">
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {question.subject}
        </span>
      </td>
      <td className="py-4 px-6">
        <span
          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(question.difficulty)}`}
        >
          {question.difficulty}
        </span>
      </td>
      <td className="py-4 px-6 text-sm text-gray-900">{new Date(question.lastEdited).toLocaleDateString()}</td>
      <td className="py-4 px-6">
        <div className="flex items-center gap-2">
          <button onClick={onPreview} className="p-1 rounded hover:bg-gray-100 transition-colors" title="Preview">
            <Eye className="h-4 w-4 text-gray-500" />
          </button>
          <div className="relative">
            <button
              onClick={() => setShowDropdown(!showDropdown)}
              className="p-1 rounded hover:bg-gray-100 transition-colors"
            >
              <MoreHorizontal className="h-4 w-4 text-gray-500" />
            </button>
            {showDropdown && (
              <div className="absolute right-0 top-8 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                <div className="py-1">
                  <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                    <Edit className="h-4 w-4" />
                    Edit Question
                  </button>
                  <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                    <Copy className="h-4 w-4" />
                    Duplicate
                  </button>
                  <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                    <Tag className="h-4 w-4" />
                    Manage Tags
                  </button>
                  <button className="flex items-center gap-2 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                    <Trash2 className="h-4 w-4" />
                    Delete
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </td>
    </tr>
  )
}

function CreateQuestionModal({
  questionType,
  setQuestionType,
  onClose,
}: {
  questionType: string
  setQuestionType: (type: string) => void
  onClose: () => void
}) {
  const [options, setOptions] = useState(["", "", "", ""])
  const [correctAnswer, setCorrectAnswer] = useState(0)

  const addOption = () => {
    setOptions([...options, ""])
  }

  const removeOption = (index: number) => {
    if (options.length > 2) {
      setOptions(options.filter((_, i) => i !== index))
      if (correctAnswer >= index && correctAnswer > 0) {
        setCorrectAnswer(correctAnswer - 1)
      }
    }
  }

  const updateOption = (index: number, value: string) => {
    const newOptions = [...options]
    newOptions[index] = value
    setOptions(newOptions)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Create New Question</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-5 w-5" />
          </button>
        </div>

        <form className="space-y-6">
          {/* Question Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Question Type</label>
            <select
              value={questionType}
              onChange={(e) => setQuestionType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="multiple-choice">Multiple Choice</option>
              <option value="essay">Essay</option>
              <option value="true-false">True/False</option>
              <option value="fill-blank">Fill in the Blank</option>
              <option value="matching">Matching</option>
            </select>
          </div>

          {/* Basic Info */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Subject</label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                {subjects.map((subject) => (
                  <option key={subject} value={subject}>
                    {subject}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Difficulty</label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                {difficulties.map((difficulty) => (
                  <option key={difficulty} value={difficulty}>
                    {difficulty}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Question Text */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Question Text
              {questionType === "fill-blank" && <span className="text-gray-500"> (Use ___ for blanks)</span>}
            </label>
            <textarea
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter your question here..."
            />
          </div>

          {/* Question Type Specific Fields */}
          {questionType === "multiple-choice" && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Answer Options</label>
              <div className="space-y-2">
                {options.map((option, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <input
                      type="radio"
                      name="correct"
                      checked={correctAnswer === index}
                      onChange={() => setCorrectAnswer(index)}
                      className="h-4 w-4 text-blue-600"
                    />
                    <input
                      type="text"
                      value={option}
                      onChange={(e) => updateOption(index, e.target.value)}
                      placeholder={`Option ${index + 1}`}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    {options.length > 2 && (
                      <button
                        type="button"
                        onClick={() => removeOption(index)}
                        className="p-2 text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                ))}
                {options.length < 6 && (
                  <button type="button" onClick={addOption} className="text-blue-600 hover:text-blue-700 text-sm">
                    + Add Option
                  </button>
                )}
              </div>
            </div>
          )}

          {questionType === "true-false" && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Correct Answer</label>
              <div className="flex gap-4">
                <label className="flex items-center">
                  <input type="radio" name="tfAnswer" value="true" className="h-4 w-4 text-blue-600" />
                  <span className="ml-2">True</span>
                </label>
                <label className="flex items-center">
                  <input type="radio" name="tfAnswer" value="false" className="h-4 w-4 text-blue-600" />
                  <span className="ml-2">False</span>
                </label>
              </div>
            </div>
          )}

          {questionType === "essay" && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Word Limit (optional)</label>
              <input
                type="number"
                min="1"
                placeholder="e.g., 300"
                className="w-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          )}

          {questionType === "fill-blank" && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Acceptable Answers</label>
              <textarea
                rows={2}
                placeholder="Enter acceptable answers separated by commas (e.g., x², x^2, x squared)"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          )}

          {/* Tags */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Tags</label>
            <input
              type="text"
              placeholder="Enter tags separated by commas"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Explanation */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Explanation (optional)</label>
            <textarea
              rows={3}
              placeholder="Provide an explanation for the correct answer..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button type="submit" className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              Create Question
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

function ImportModal({ onClose }: { onClose: () => void }) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Import Questions</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="space-y-6">
          {/* Import Options */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Import Format</label>
            <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="csv">CSV File</option>
              <option value="excel">Excel File</option>
              <option value="qti">QTI Standard</option>
              <option value="word">Word Document</option>
            </select>
          </div>

          {/* File Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Upload File</label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600">Drop your file here or click to browse</p>
              <input type="file" className="hidden" />
            </div>
          </div>

          {/* Column Mapping */}
          <div className="bg-blue-50 rounded-md p-4">
            <h4 className="text-sm font-medium text-blue-800 mb-2">CSV Column Mapping Guide:</h4>
            <div className="text-xs text-blue-700 space-y-1">
              <div>• Column A: Question Text</div>
              <div>• Column B: Question Type (multiple-choice, essay, true-false, etc.)</div>
              <div>• Column C: Subject</div>
              <div>• Column D: Difficulty (Easy, Medium, Hard)</div>
              <div>• Column E: Option 1 (for multiple choice)</div>
              <div>• Column F: Option 2 (for multiple choice)</div>
              <div>• Column G: Correct Answer</div>
              <div>• Column H: Tags (comma-separated)</div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button type="submit" className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              Import Questions
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

function PreviewModal({ question, onClose }: { question: any; onClose: () => void }) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Question Preview</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="space-y-6">
          {/* Question Header */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="text-sm font-mono text-gray-600">{question.id}</div>
              <span
                className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${question.difficulty === "Easy" ? "bg-green-100 text-green-800" : question.difficulty === "Medium" ? "bg-yellow-100 text-yellow-800" : "bg-red-100 text-red-800"}`}
              >
                {question.difficulty}
              </span>
            </div>
            <h3 className="text-lg font-medium text-gray-900">{question.title}</h3>
            <div className="flex items-center gap-2 mt-2">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {question.subject}
              </span>
              <span className="text-sm text-gray-600 capitalize">{question.type.replace("-", " ")}</span>
            </div>
          </div>

          {/* Question Content */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Question</h4>
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <p className="text-gray-900">{question.text}</p>
            </div>
          </div>

          {/* Answer Options/Content */}
          {question.type === "multiple-choice" && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Answer Options</h4>
              <div className="space-y-2">
                {question.options.map((option: string, index: number) => (
                  <div
                    key={index}
                    className={`p-3 border rounded-lg ${index === question.correctAnswer ? "border-green-500 bg-green-50" : "border-gray-200"}`}
                  >
                    <div className="flex items-center">
                      <div
                        className={`h-4 w-4 rounded-full border ${index === question.correctAnswer ? "border-green-500 bg-green-500" : "border-gray-300"} flex items-center justify-center mr-3`}
                      >
                        {index === question.correctAnswer && <div className="h-2 w-2 rounded-full bg-white"></div>}
                      </div>
                      <span className="text-gray-900">{option}</span>
                      {index === question.correctAnswer && (
                        <span className="ml-auto text-green-600 text-sm font-medium">Correct</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {question.type === "true-false" && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Correct Answer</h4>
              <div className="p-3 border border-green-500 bg-green-50 rounded-lg">
                <span className="font-medium text-green-800">{question.correctAnswer ? "True" : "False"}</span>
              </div>
            </div>
          )}

          {question.type === "fill-blank" && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Acceptable Answers</h4>
              <div className="p-3 border border-gray-200 rounded-lg">
                <div className="flex flex-wrap gap-2">
                  {question.acceptableAnswers.map((answer: string, index: number) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800"
                    >
                      {answer}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}

          {question.type === "essay" && question.wordLimit && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Word Limit</h4>
              <div className="p-3 border border-gray-200 rounded-lg">
                <span className="text-gray-900">{question.wordLimit} words</span>
              </div>
            </div>
          )}

          {/* Tags */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Tags</h4>
            <div className="flex flex-wrap gap-2">
              {question.tags.map((tag: string) => (
                <span
                  key={tag}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>

          {/* Metadata */}
          <div className="text-sm text-gray-600">
            <p>Last edited: {new Date(question.lastEdited).toLocaleDateString()}</p>
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4 border-t">
            <button className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors">
              <Edit className="h-4 w-4 inline mr-2" />
              Edit Question
            </button>
            <button className="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors">
              <Copy className="h-4 w-4 inline mr-2" />
              Duplicate
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
